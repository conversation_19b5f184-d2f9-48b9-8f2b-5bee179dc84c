# HeartGrid Dating Platform - Minimal Requirements
# Based on pyproject.toml and actual usage

# Core Django Framework
Django>=5.0.0
djangorestframework>=3.14.0

# Database & Configuration
dj-database-url>=2.1.0
python-decouple>=3.8

# Authentication & Authorization
django-allauth>=0.57.0
django-oauth-toolkit>=1.7.0

# WebSocket & Real-time Communications (for messaging and WebRTC)
channels>=4.0.0
channels-redis>=4.1.0
daphne>=4.0.0

# API & CORS
django-cors-headers>=4.3.0

# Image Processing (for user photos)
Pillow>=11.2.1

# HTTP Requests (for external APIs)
requests>=2.32.4

# Static Files & Media
whitenoise>=6.6.0

# Production Server
gunicorn>=23.0.0

# Background Tasks (for async processing)
celery>=5.3.0
redis>=5.0.0
