{% extends "base.html" %}

{% block title %}Notifications - HeartGrid{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row">
        <div class="col-md-8 mx-auto">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">Notifications</h1>
                {% if unread_count > 0 %}
                <button class="btn btn-outline-primary btn-sm" onclick="markAllRead()">
                    Mark all read ({{ unread_count }})
                </button>
                {% endif %}
            </div>
            
            <!-- Notification List -->
            <div class="notifications-list">
                {% for notification in notifications %}
                <div class="notification-item {{ 'unread' if not notification.read }}" data-id="{{ notification.id }}">
                    <div class="d-flex align-items-start">
                        <div class="notification-icon me-3">
                            {% if notification.type == 'new_match' %}
                            <i class="fas fa-heart text-danger"></i>
                            {% elif notification.type == 'new_message' %}
                            <i class="fas fa-comment text-primary"></i>
                            {% elif notification.type == 'new_like' %}
                            <i class="fas fa-thumbs-up text-success"></i>
                            {% elif notification.type == 'super_like_received' %}
                            <i class="fas fa-star text-warning"></i>
                            {% elif notification.type == 'achievement_unlocked' %}
                            <i class="fas fa-trophy text-warning"></i>
                            {% elif notification.type == 'daily_challenge' %}
                            <i class="fas fa-calendar-check text-info"></i>
                            {% else %}
                            <i class="fas fa-bell text-muted"></i>
                            {% endif %}
                        </div>
                        
                        <div class="notification-content flex-grow-1">
                            <h6 class="notification-title mb-1">{{ notification.title }}</h6>
                            <p class="notification-message mb-1">{{ notification.message }}</p>
                            <small class="notification-time text-muted">{{ notification.created_at[:16] }}</small>
                        </div>
                        
                        <div class="notification-actions">
                            {% if not notification.read %}
                            <button class="btn btn-sm btn-outline-secondary" onclick="markRead('{{ notification.id }}')">
                                Mark read
                            </button>
                            {% endif %}
                        </div>
                    </div>
                </div>
                {% empty %}
                <div class="text-center py-5">
                    <i class="fas fa-bell-slash fa-3x text-muted mb-3"></i>
                    <p class="text-muted">No notifications yet</p>
                </div>
                {% endfor %}
            </div>
            
            <!-- Notification Preferences -->
            <div class="card border-0 shadow-sm mt-5">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-cog me-2"></i>Notification Preferences
                    </h5>
                </div>
                <div class="card-body">
                    <form id="preferencesForm">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <h6 class="fw-bold mb-3">Notification Types</h6>
                                
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" id="new_match" name="new_match" 
                                           {{ 'checked' if preferences.new_match }}>
                                    <label class="form-check-label" for="new_match">
                                        New Matches
                                    </label>
                                </div>
                                
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" id="new_message" name="new_message" 
                                           {{ 'checked' if preferences.new_message }}>
                                    <label class="form-check-label" for="new_message">
                                        New Messages
                                    </label>
                                </div>
                                
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" id="new_like" name="new_like" 
                                           {{ 'checked' if preferences.new_like }}>
                                    <label class="form-check-label" for="new_like">
                                        New Likes
                                    </label>
                                </div>
                                
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" id="super_like_received" name="super_like_received" 
                                           {{ 'checked' if preferences.super_like_received }}>
                                    <label class="form-check-label" for="super_like_received">
                                        Super Likes
                                    </label>
                                </div>
                                
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" id="achievement_unlocked" name="achievement_unlocked" 
                                           {{ 'checked' if preferences.achievement_unlocked }}>
                                    <label class="form-check-label" for="achievement_unlocked">
                                        Achievements
                                    </label>
                                </div>
                                
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" id="daily_challenge" name="daily_challenge" 
                                           {{ 'checked' if preferences.daily_challenge }}>
                                    <label class="form-check-label" for="daily_challenge">
                                        Daily Challenges
                                    </label>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <h6 class="fw-bold mb-3">General Settings</h6>
                                
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="push_enabled" name="push_enabled" 
                                           {{ 'checked' if preferences.push_enabled }}>
                                    <label class="form-check-label" for="push_enabled">
                                        Enable Push Notifications
                                    </label>
                                </div>
                                
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="email_enabled" name="email_enabled" 
                                           {{ 'checked' if preferences.email_enabled }}>
                                    <label class="form-check-label" for="email_enabled">
                                        Enable Email Notifications
                                    </label>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="quiet_hours_start" class="form-label">Quiet Hours Start</label>
                                    <input type="time" class="form-control" id="quiet_hours_start" name="quiet_hours_start" 
                                           value="{{ preferences.quiet_hours_start }}">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="quiet_hours_end" class="form-label">Quiet Hours End</label>
                                    <input type="time" class="form-control" id="quiet_hours_end" name="quiet_hours_end" 
                                           value="{{ preferences.quiet_hours_end }}">
                                </div>
                            </div>
                        </div>
                        
                        <div class="text-center mt-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Save Preferences
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.notification-item {
    padding: 1rem;
    border-bottom: 1px solid #f1f1f1;
    transition: all 0.3s ease;
}

.notification-item:hover {
    background-color: #f8f9fa;
}

.notification-item.unread {
    border-left: 4px solid #007bff;
    background-color: #f8f9ff;
}

.notification-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
}

.notification-title {
    font-weight: 600;
    color: #333;
}

.notification-message {
    color: #666;
    margin-bottom: 0.25rem;
}

.notification-time {
    font-size: 0.875rem;
}

.notifications-list {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    overflow: hidden;
}
</style>

<script>
function markRead(notificationId) {
    fetch(`/notifications/mark_read/${notificationId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const notificationElement = document.querySelector(`[data-id="${notificationId}"]`);
            notificationElement.classList.remove('unread');
            notificationElement.querySelector('.notification-actions').innerHTML = '';
            
            // Update unread count
            const unreadCount = document.querySelectorAll('.notification-item.unread').length;
            if (unreadCount === 0) {
                location.reload(); // Refresh to update header
            }
        }
    })
    .catch(error => {
        showNotification('Error marking notification as read', 'error');
    });
}

function markAllRead() {
    fetch('/notifications/mark_all_read', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        }
    })
    .catch(error => {
        showNotification('Error marking all notifications as read', 'error');
    });
}

document.getElementById('preferencesForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const preferences = {};
    
    // Get all form values
    for (let [key, value] of formData.entries()) {
        preferences[key] = true;
    }
    
    // Add unchecked checkboxes as false
    const checkboxes = this.querySelectorAll('input[type="checkbox"]');
    checkboxes.forEach(checkbox => {
        if (!checkbox.checked) {
            preferences[checkbox.name] = false;
        }
    });
    
    // Add time inputs
    preferences['quiet_hours_start'] = document.getElementById('quiet_hours_start').value;
    preferences['quiet_hours_end'] = document.getElementById('quiet_hours_end').value;
    
    fetch('/notifications/preferences', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(preferences)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Notification preferences updated!', 'success');
        }
    })
    .catch(error => {
        showNotification('Error updating preferences', 'error');
    });
});
</script>
{% endblock %}