/**
 * HeartGrid Communications - Real-time Messaging
 * 
 * This module handles WebSocket connections for real-time messaging,
 * user presence tracking, and notifications.
 */

class HeartGridCommunications {
    constructor() {
        this.chatSocket = null;
        this.presenceSocket = null;
        this.currentConversationId = null;
        this.currentUser = null;
        this.typingTimer = null;
        this.isTyping = false;
        
        this.init();
    }

    init() {
        // Get current user info from DOM or API
        this.getCurrentUser();
        
        // Initialize presence tracking
        this.initPresenceSocket();
        
        // Bind event listeners
        this.bindEvents();
        
        // Start heartbeat for presence
        this.startHeartbeat();
    }

    getCurrentUser() {
        // Try to get user info from a global variable or data attribute
        const userElement = document.querySelector('[data-user-id]');
        if (userElement) {
            this.currentUser = {
                id: userElement.dataset.userId,
                name: userElement.dataset.userName || 'User'
            };
        }
    }

    initPresenceSocket() {
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${protocol}//${window.location.host}/ws/presence/`;
        
        this.presenceSocket = new WebSocket(wsUrl);
        
        this.presenceSocket.onopen = () => {
            console.log('Presence WebSocket connected');
        };
        
        this.presenceSocket.onmessage = (event) => {
            const data = JSON.parse(event.data);
            this.handlePresenceMessage(data);
        };
        
        this.presenceSocket.onclose = () => {
            console.log('Presence WebSocket disconnected');
            // Attempt to reconnect after 3 seconds
            setTimeout(() => this.initPresenceSocket(), 3000);
        };
        
        this.presenceSocket.onerror = (error) => {
            console.error('Presence WebSocket error:', error);
        };
    }

    initChatSocket(conversationId) {
        // Close existing chat socket if any
        if (this.chatSocket) {
            this.chatSocket.close();
        }
        
        this.currentConversationId = conversationId;
        
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${protocol}//${window.location.host}/ws/chat/${conversationId}/`;
        
        this.chatSocket = new WebSocket(wsUrl);
        
        this.chatSocket.onopen = () => {
            console.log('Chat WebSocket connected for conversation:', conversationId);
        };
        
        this.chatSocket.onmessage = (event) => {
            const data = JSON.parse(event.data);
            this.handleChatMessage(data);
        };
        
        this.chatSocket.onclose = () => {
            console.log('Chat WebSocket disconnected');
        };
        
        this.chatSocket.onerror = (error) => {
            console.error('Chat WebSocket error:', error);
        };
    }

    handlePresenceMessage(data) {
        switch (data.type) {
            case 'presence_update':
                this.updateUserPresence(data.user_id, data.status, data.last_seen);
                break;
        }
    }

    handleChatMessage(data) {
        switch (data.type) {
            case 'chat_message':
                this.displayMessage(data.message);
                break;
            case 'typing_status':
                this.updateTypingStatus(data.user_id, data.user_name, data.is_typing);
                break;
            case 'user_status':
                this.updateUserPresence(data.user_id, data.status, data.last_seen);
                break;
            case 'message_reaction':
                this.updateMessageReactions(data.message_id, data.reactions);
                break;
            case 'error':
                console.error('Chat error:', data.message);
                break;
        }
    }

    sendMessage(content, messageType = 'text') {
        if (!this.chatSocket || this.chatSocket.readyState !== WebSocket.OPEN) {
            console.error('Chat socket not connected');
            return;
        }
        
        this.chatSocket.send(JSON.stringify({
            type: 'chat_message',
            message: content,
            message_type: messageType
        }));
    }

    startTyping(toUserId) {
        if (!this.isTyping) {
            this.isTyping = true;
            
            // Send typing start to chat socket
            if (this.chatSocket && this.chatSocket.readyState === WebSocket.OPEN) {
                this.chatSocket.send(JSON.stringify({
                    type: 'typing_start'
                }));
            }
        }
        
        // Reset typing timer
        clearTimeout(this.typingTimer);
        this.typingTimer = setTimeout(() => {
            this.stopTyping();
        }, 3000); // Stop typing after 3 seconds of inactivity
    }

    stopTyping() {
        if (this.isTyping) {
            this.isTyping = false;
            
            // Send typing stop to chat socket
            if (this.chatSocket && this.chatSocket.readyState === WebSocket.OPEN) {
                this.chatSocket.send(JSON.stringify({
                    type: 'typing_stop'
                }));
            }
        }
        
        clearTimeout(this.typingTimer);
    }

    markMessageAsRead(messageId) {
        if (!this.chatSocket || this.chatSocket.readyState !== WebSocket.OPEN) {
            return;
        }
        
        this.chatSocket.send(JSON.stringify({
            type: 'mark_read',
            message_id: messageId
        }));
    }

    addMessageReaction(messageId, emoji) {
        if (!this.chatSocket || this.chatSocket.readyState !== WebSocket.OPEN) {
            return;
        }
        
        this.chatSocket.send(JSON.stringify({
            type: 'message_reaction',
            message_id: messageId,
            emoji: emoji,
            action: 'add'
        }));
    }

    removeMessageReaction(messageId, emoji) {
        if (!this.chatSocket || this.chatSocket.readyState !== WebSocket.OPEN) {
            return;
        }
        
        this.chatSocket.send(JSON.stringify({
            type: 'message_reaction',
            message_id: messageId,
            emoji: emoji,
            action: 'remove'
        }));
    }

    displayMessage(message) {
        const messagesContainer = document.getElementById('messages-container');
        if (!messagesContainer) return;
        
        const messageElement = this.createMessageElement(message);
        messagesContainer.appendChild(messageElement);
        
        // Scroll to bottom
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
        
        // Play notification sound if message is from another user
        if (message.sender_id !== this.currentUser?.id) {
            this.playNotificationSound();
            this.showNotification(message);
        }
    }

    createMessageElement(message) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${message.sender_id === this.currentUser?.id ? 'sent' : 'received'}`;
        messageDiv.dataset.messageId = message.id;
        
        const timestamp = new Date(message.created_at).toLocaleTimeString();
        
        messageDiv.innerHTML = `
            <div class="message-content">
                <div class="message-text">${this.escapeHtml(message.content)}</div>
                <div class="message-meta">
                    <span class="message-sender">${this.escapeHtml(message.sender_name)}</span>
                    <span class="message-time">${timestamp}</span>
                </div>
                <div class="message-reactions" data-message-id="${message.id}">
                    ${this.renderReactions(message.reactions)}
                </div>
            </div>
        `;
        
        return messageDiv;
    }

    renderReactions(reactions) {
        if (!reactions || Object.keys(reactions).length === 0) {
            return '';
        }
        
        const reactionCounts = {};
        Object.values(reactions).forEach(emoji => {
            reactionCounts[emoji] = (reactionCounts[emoji] || 0) + 1;
        });
        
        return Object.entries(reactionCounts)
            .map(([emoji, count]) => `<span class="reaction">${emoji} ${count}</span>`)
            .join('');
    }

    updateTypingStatus(userId, userName, isTyping) {
        const typingIndicator = document.getElementById('typing-indicator');
        if (!typingIndicator) return;
        
        if (isTyping) {
            typingIndicator.textContent = `${userName} is typing...`;
            typingIndicator.style.display = 'block';
        } else {
            typingIndicator.style.display = 'none';
        }
    }

    updateUserPresence(userId, status, lastSeen) {
        const presenceElements = document.querySelectorAll(`[data-user-presence="${userId}"]`);
        presenceElements.forEach(element => {
            element.className = `presence-indicator ${status}`;
            element.title = status === 'offline' && lastSeen 
                ? `Last seen: ${new Date(lastSeen).toLocaleString()}`
                : status;
        });
    }

    updateMessageReactions(messageId, reactions) {
        const reactionsContainer = document.querySelector(`[data-message-id="${messageId}"] .message-reactions`);
        if (reactionsContainer) {
            reactionsContainer.innerHTML = this.renderReactions(reactions);
        }
    }

    bindEvents() {
        // Message input events
        const messageInput = document.getElementById('message-input');
        if (messageInput) {
            messageInput.addEventListener('input', () => {
                this.startTyping();
            });
            
            messageInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    this.sendMessageFromInput();
                }
            });
        }
        
        // Send button
        const sendButton = document.getElementById('send-button');
        if (sendButton) {
            sendButton.addEventListener('click', () => {
                this.sendMessageFromInput();
            });
        }
        
        // Message reactions
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('reaction-button')) {
                const messageId = e.target.dataset.messageId;
                const emoji = e.target.dataset.emoji;
                this.addMessageReaction(messageId, emoji);
            }
        });
    }

    sendMessageFromInput() {
        const messageInput = document.getElementById('message-input');
        if (!messageInput) return;
        
        const content = messageInput.value.trim();
        if (content) {
            this.sendMessage(content);
            messageInput.value = '';
            this.stopTyping();
        }
    }

    startHeartbeat() {
        setInterval(() => {
            if (this.presenceSocket && this.presenceSocket.readyState === WebSocket.OPEN) {
                this.presenceSocket.send(JSON.stringify({
                    type: 'heartbeat'
                }));
            }
        }, 30000); // Send heartbeat every 30 seconds
    }

    playNotificationSound() {
        // Create and play notification sound
        const audio = new Audio('/static/sounds/notification.mp3');
        audio.volume = 0.3;
        audio.play().catch(() => {
            // Ignore errors if sound can't be played
        });
    }

    showNotification(message) {
        if ('Notification' in window && Notification.permission === 'granted') {
            new Notification(`New message from ${message.sender_name}`, {
                body: message.content,
                icon: '/static/images/heartgrid-icon.png'
            });
        }
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // Public methods for external use
    openConversation(conversationId) {
        this.initChatSocket(conversationId);
    }

    closeConversation() {
        if (this.chatSocket) {
            this.chatSocket.close();
            this.chatSocket = null;
        }
        this.currentConversationId = null;
    }

    requestNotificationPermission() {
        if ('Notification' in window && Notification.permission === 'default') {
            Notification.requestPermission();
        }
    }
}

// Initialize communications when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.heartGridComms = new HeartGridCommunications();
    
    // Request notification permission
    window.heartGridComms.requestNotificationPermission();
});
