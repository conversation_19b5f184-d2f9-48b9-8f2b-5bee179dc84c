{% extends "base.html" %}

{% block title %}Sign Up - HeartGrid{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <div class="auth-card">
                <div class="text-center mb-4">
                    <i class="fas fa-heart fa-3x text-danger mb-3 heartbeat"></i>
                    <h2 class="fw-bold">Join HeartGrid</h2>
                    <p class="text-muted">Start your journey to find love</p>
                </div>
                
                <form method="POST" class="needs-validation" novalidate>
                    {% csrf_token %}
                    <div class="mb-3">
                        <label for="name" class="form-label">Full Name</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-user"></i>
                            </span>
                            <input type="text" class="form-control" id="name" name="name" minlength="2" maxlength="50" required>
                            <div class="invalid-feedback">
                                Please enter your full name (2-50 characters).
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="email" class="form-label">Email Address</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-envelope"></i>
                            </span>
                            <input type="email" class="form-control" id="email" name="email" required>
                            <div class="invalid-feedback">
                                Please enter a valid email address.
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="date_of_birth" class="form-label">Date of Birth</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-calendar"></i>
                            </span>
                            <input type="date" class="form-control" id="date_of_birth" name="date_of_birth" required>
                            <div class="invalid-feedback">
                                Please enter your date of birth. You must be 18 or older.
                            </div>
                        </div>
                        <div class="form-text">
                            <i class="fas fa-info-circle me-1"></i>
                            You must be 18 years or older to join HeartGrid
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <label for="password" class="form-label">Password</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-lock"></i>
                            </span>
                            <input type="password" class="form-control" id="password" name="password" 
                                   minlength="8" pattern="^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{8,}$" required>
                            <div class="invalid-feedback">
                                Password must be at least 8 characters with uppercase, lowercase, and number.
                            </div>
                        </div>
                        <div class="form-text">
                            <i class="fas fa-info-circle me-1"></i>
                            Must contain: 8+ characters, uppercase, lowercase, and number
                        </div>
                    </div>
                    
                    <button type="submit" class="btn btn-primary w-100 btn-lg mb-3">
                        <i class="fas fa-heart me-2"></i>
                        Create Account
                    </button>
                </form>
                
                <div class="text-center">
                    <p class="text-muted">
                        Already have an account?
                        <a href="{% url 'heartgrid_frontend:login_page' %}" class="text-primary fw-bold">Sign in here</a>
                    </p>
                </div>
            </div>
        </div>
        
        <div class="col-md-6 d-none d-md-block">
            <div class="auth-image">
                <img src="https://pixabay.com/get/g187f49d995762a527ea82f36cc25ea0b1823163d2904ea8892735effad6b2f80c4a02bad278b5d34669db861454aeb6fb7ccf77a0596a5a5be64f4834a590899_1280.jpg" 
                     alt="Dating Lifestyle" class="img-fluid rounded-lg">
                <div class="auth-overlay">
                    <h4 class="text-white fw-bold mb-2">Start Your Love Story</h4>
                    <p class="text-white">Create your profile and meet amazing people</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
