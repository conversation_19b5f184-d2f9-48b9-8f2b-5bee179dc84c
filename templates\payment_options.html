{% extends "base.html" %}

{% block title %}Payment Options - HeartGrid{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="text-center mb-5">
                <h1 class="display-5 fw-bold text-primary">Complete Your Purchase</h1>
                <p class="lead text-muted">Choose your preferred payment method</p>
            </div>

            <!-- Selected Plan Summary -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h5 class="mb-1">{{ plan.title() }} Plan</h5>
                            <p class="text-muted mb-0">
                                {% if plan == 'weekly' %}
                                7 days of premium access
                                {% elif plan == 'fortnightly' %}
                                14 days of premium access - Most Popular
                                {% else %}
                                30 days of premium access - Best Value
                                {% endif %}
                            </p>
                        </div>
                        <div class="col-md-4 text-end">
                            <h3 class="text-primary mb-0">${{ plan_details.price }}</h3>
                            <small class="text-muted">
                                {% if plan == 'weekly' %}
                                per week
                                {% elif plan == 'fortnightly' %}
                                per 2 weeks
                                {% else %}
                                per month
                                {% endif %}
                            </small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payment Methods -->
            <div class="row g-4">
                <!-- Cryptocurrency Payment -->
                <div class="col-md-6">
                    <div class="card payment-method-card border-0 shadow-sm h-100">
                        <div class="card-body text-center p-4">
                            <div class="payment-icon mb-3">
                                <i class="fab fa-bitcoin fa-3x text-warning"></i>
                            </div>
                            <h5 class="fw-bold mb-2">Cryptocurrency</h5>
                            <p class="text-muted mb-3">Pay with Bitcoin, USDT, ETH, BNB, SOL, TRX, or TON</p>
                            
                            <div class="payment-benefits mb-3">
                                <small class="text-success d-block"><i class="fas fa-check me-1"></i>Instant activation</small>
                                <small class="text-success d-block"><i class="fas fa-check me-1"></i>Anonymous payments</small>
                                <small class="text-success d-block"><i class="fas fa-check me-1"></i>Global accessibility</small>
                                <small class="text-warning d-block"><i class="fas fa-star me-1"></i>5% discount for crypto payments</small>
                            </div>
                            
                            <a href="/api/v1/subscription/crypto/?plan={{ plan }}" class="btn btn-warning w-100 btn-lg">
                                <i class="fab fa-bitcoin me-2"></i>Pay with Crypto
                            </a>

                            <!-- NOWPayments Integration for All Plans -->
                            {% if plan == 'weekly' %}
                            <div class="nowpayments-section mt-3">
                                <div class="text-center mb-2">
                                    <small class="text-muted">Alternative Crypto Payment</small>
                                </div>
                                <a href="https://nowpayments.io/payment/?iid=5532080560&source=button"
                                   target="_blank"
                                   rel="noreferrer noopener"
                                   class="btn btn-outline-warning w-100 nowpayments-btn">
                                    <img src="https://nowpayments.io/images/embeds/payment-button-white.svg"
                                         alt="Cryptocurrency & Bitcoin payment button by NOWPayments"
                                         class="nowpayments-icon me-2">
                                    Pay with NOWPayments
                                </a>
                                <div class="text-center mt-2">
                                    <small class="text-muted">
                                        <i class="fas fa-shield-alt me-1"></i>Secure & Fast
                                        <i class="fas fa-globe ms-2 me-1"></i>Global Access
                                    </small>
                                </div>
                            </div>
                            {% elif plan == 'fortnightly' %}
                            <div class="nowpayments-section mt-3">
                                <div class="text-center mb-2">
                                    <small class="text-muted">Alternative Crypto Payment</small>
                                </div>
                                <a href="https://nowpayments.io/payment/?iid=4564770173&source=button"
                                   target="_blank"
                                   rel="noreferrer noopener"
                                   class="btn btn-outline-warning w-100 nowpayments-btn">
                                    <img src="https://nowpayments.io/images/embeds/payment-button-white.svg"
                                         alt="Cryptocurrency & Bitcoin payment button by NOWPayments"
                                         class="nowpayments-icon me-2">
                                    Pay with NOWPayments
                                </a>
                                <div class="text-center mt-2">
                                    <small class="text-muted">
                                        <i class="fas fa-shield-alt me-1"></i>Secure & Fast
                                        <i class="fas fa-globe ms-2 me-1"></i>Global Access
                                    </small>
                                </div>
                            </div>
                            {% elif plan == 'monthly' %}
                            <div class="nowpayments-section mt-3">
                                <div class="text-center mb-2">
                                    <small class="text-muted">Alternative Crypto Payment</small>
                                </div>
                                <a href="https://nowpayments.io/payment/?iid=5119273624&source=button"
                                   target="_blank"
                                   rel="noreferrer noopener"
                                   class="btn btn-outline-warning w-100 nowpayments-btn">
                                    <img src="https://nowpayments.io/images/embeds/payment-button-white.svg"
                                         alt="Cryptocurrency & Bitcoin payment button by NOWPayments"
                                         class="nowpayments-icon me-2">
                                    Pay with NOWPayments
                                </a>
                                <div class="text-center mt-2">
                                    <small class="text-muted">
                                        <i class="fas fa-shield-alt me-1"></i>Secure & Fast
                                        <i class="fas fa-globe ms-2 me-1"></i>Global Access
                                    </small>
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Traditional Payment -->
                <div class="col-md-6">
                    <div class="card payment-method-card border-0 shadow-sm h-100">
                        <div class="card-body text-center p-4">
                            <div class="payment-icon mb-3">
                                <i class="fas fa-credit-card fa-3x text-primary"></i>
                            </div>
                            <h5 class="fw-bold mb-2">Credit/Debit Card</h5>
                            <p class="text-muted mb-3">Pay with Visa, Mastercard, or American Express</p>
                            
                            <div class="payment-benefits mb-3">
                                <small class="text-success d-block"><i class="fas fa-check me-1"></i>Secure payments</small>
                                <small class="text-success d-block"><i class="fas fa-check me-1"></i>Automatic renewal</small>
                                <small class="text-success d-block"><i class="fas fa-check me-1"></i>Easy cancellation</small>
                                <small class="text-info d-block"><i class="fas fa-shield-alt me-1"></i>SSL encrypted</small>
                            </div>
                            
                            <button class="btn btn-primary w-100 btn-lg" onclick="showStripePayment()">
                                <i class="fas fa-credit-card me-2"></i>Pay with Card
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payment Security Info -->
            <div class="text-center mt-4">
                <div class="security-badges">
                    <span class="badge bg-light text-dark me-2">
                        <i class="fas fa-shield-alt text-success me-1"></i>SSL Secured
                    </span>
                    <span class="badge bg-light text-dark me-2">
                        <i class="fas fa-lock text-primary me-1"></i>256-bit Encryption
                    </span>
                    <span class="badge bg-light text-dark">
                        <i class="fas fa-check-circle text-success me-1"></i>PCI Compliant
                    </span>
                </div>
                <p class="text-muted small mt-2">
                    Your payment information is secure and protected. You can cancel anytime.
                </p>
            </div>

            <!-- Back to Plans -->
            <div class="text-center mt-4">
                <a href="{% url 'heartgrid_frontend:subscription_page' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Plans
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Stripe Payment Modal -->
<div class="modal fade" id="stripeModal" tabindex="-1">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Complete Payment</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Note:</strong> This is a demo environment. Card payments are simulated.
                    For real payments, integrate with Stripe, PayPal, or other payment processors.
                </div>
                
                <form id="stripePaymentForm">
                    <div class="row g-3">
                        <div class="col-12">
                            <label for="cardNumber" class="form-label">Card Number</label>
                            <input type="text" class="form-control" id="cardNumber" placeholder="4242 4242 4242 4242" maxlength="19">
                        </div>
                        <div class="col-md-6">
                            <label for="expiry" class="form-label">Expiry Date</label>
                            <input type="text" class="form-control" id="expiry" placeholder="MM/YY" maxlength="5">
                        </div>
                        <div class="col-md-6">
                            <label for="cvc" class="form-label">CVC</label>
                            <input type="text" class="form-control" id="cvc" placeholder="123" maxlength="4">
                        </div>
                        <div class="col-12">
                            <label for="cardName" class="form-label">Cardholder Name</label>
                            <input type="text" class="form-control" id="cardName" placeholder="John Doe">
                        </div>
                    </div>
                    
                    <div class="mt-4">
                        <button type="submit" class="btn btn-primary w-100 btn-lg">
                            <i class="fas fa-credit-card me-2"></i>
                            Pay ${{ plan_details.price }} - {{ plan.title() }}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<style>
.payment-method-card {
    transition: all 0.3s ease;
    cursor: pointer;
}

.payment-method-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.15) !important;
}

.payment-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: rgba(13, 110, 253, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
}

.payment-benefits {
    text-align: left;
}

.security-badges .badge {
    font-size: 0.875rem;
    padding: 0.5rem 0.75rem;
}

/* NOWPayments Button Styling */
.nowpayments-section {
    border-top: 1px solid #e9ecef;
    padding-top: 15px;
    margin-top: 15px;
}

.nowpayments-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 12px 20px;
    border: 2px solid #ffc107;
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
    text-decoration: none;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.nowpayments-btn:hover {
    background: linear-gradient(135deg, #ffeaa7 0%, #fdcb6e 100%);
    color: #6c5ce7;
    border-color: #e17055;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(255, 193, 7, 0.3);
    text-decoration: none;
}

.nowpayments-icon {
    height: 20px;
    width: auto;
    filter: brightness(0.8);
}

.nowpayments-btn:hover .nowpayments-icon {
    filter: brightness(1);
}
</style>

<script>
function showStripePayment() {
    new bootstrap.Modal(document.getElementById('stripeModal')).show();
}

// Format card number input
document.getElementById('cardNumber').addEventListener('input', function(e) {
    let value = e.target.value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
    let formattedValue = value.match(/.{1,4}/g)?.join(' ') || value;
    e.target.value = formattedValue;
});

// Format expiry input
document.getElementById('expiry').addEventListener('input', function(e) {
    let value = e.target.value.replace(/\D/g, '');
    if (value.length >= 2) {
        value = value.substring(0, 2) + '/' + value.substring(2, 4);
    }
    e.target.value = value;
});

// Handle form submission
document.getElementById('stripePaymentForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    // Show loading state
    const submitBtn = e.target.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Processing...';
    submitBtn.disabled = true;
    
    // Simulate payment processing
    setTimeout(() => {
        // Create subscription
        fetch('/subscribe/{{ plan }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => {
            if (response.redirected) {
                window.location.href = response.url;
                return;
            }
            return response.json();
        })
        .then(data => {
            bootstrap.Modal.getInstance(document.getElementById('stripeModal')).hide();
            showNotification('Payment successful! Welcome to premium!', 'success');
            
            // Redirect after success
            setTimeout(() => {
                window.location.href = '/discover';
            }, 2000);
        })
        .catch(error => {
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
            showNotification('Payment failed. Please try again.', 'error');
        });
    }, 3000);
});
</script>
{% endblock %}