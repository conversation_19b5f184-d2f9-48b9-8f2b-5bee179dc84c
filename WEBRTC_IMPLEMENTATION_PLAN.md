# WebRTC P2P Voice & Video Calling Implementation Plan

## Overview
This document outlines the technical implementation plan for adding peer-to-peer voice and video calling functionality to the HeartGrid Communications app using WebRTC technology, building upon the existing Django Channels WebSocket infrastructure.

## 🏗️ Architecture Overview

### Current Infrastructure (Already Implemented)
- ✅ Django Channels with ASGI/Daphne server
- ✅ WebSocket consumers for real-time messaging
- ✅ User presence tracking system
- ✅ Notification system
- ✅ Database models for conversations and messages

### New WebRTC Components (To Be Added)
- 🔄 WebRTC signaling through existing WebSocket infrastructure
- 🔄 Call state management models
- 🔄 Frontend WebRTC peer connection handling
- 🔄 Call UI components and notifications
- 🔄 STUN/TURN server configuration

## 📋 Implementation Plan

### Phase 1: Backend Call State Management

#### 1.1 Database Models Extension
```python
# New models to add to communications/models.py

class CallSession(models.Model):
    CALL_TYPES = [
        ('voice', 'Voice Call'),
        ('video', 'Video Call'),
    ]
    
    CALL_STATES = [
        ('initiating', 'Initiating'),
        ('ringing', 'Ringing'),
        ('connecting', 'Connecting'),
        ('active', 'Active'),
        ('ended', 'Ended'),
        ('missed', 'Missed'),
        ('rejected', 'Rejected'),
        ('failed', 'Failed'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4)
    conversation = models.ForeignKey(Conversation, on_delete=models.CASCADE)
    caller = models.ForeignKey(User, on_delete=models.CASCADE, related_name='initiated_calls')
    callee = models.ForeignKey(User, on_delete=models.CASCADE, related_name='received_calls')
    call_type = models.CharField(max_length=10, choices=CALL_TYPES)
    state = models.CharField(max_length=15, choices=CALL_STATES, default='initiating')
    started_at = models.DateTimeField(auto_now_add=True)
    answered_at = models.DateTimeField(null=True, blank=True)
    ended_at = models.DateTimeField(null=True, blank=True)
    duration = models.DurationField(null=True, blank=True)
    end_reason = models.CharField(max_length=50, blank=True)

class UserCallSettings(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='call_settings')
    voice_calls_enabled = models.BooleanField(default=True)
    video_calls_enabled = models.BooleanField(default=True)
    auto_reject_calls = models.BooleanField(default=False)
    call_notifications_enabled = models.BooleanField(default=True)
```

#### 1.2 Extended User Presence for Call Availability
```python
# Extension to existing UserPresence model
class UserPresence(models.Model):
    # ... existing fields ...
    
    # New call-related fields
    CALL_AVAILABILITY = [
        ('available', 'Available for calls'),
        ('busy', 'Busy - Do not disturb'),
        ('in_call', 'Currently in a call'),
    ]
    
    call_availability = models.CharField(
        max_length=15, 
        choices=CALL_AVAILABILITY, 
        default='available'
    )
    current_call_session = models.ForeignKey(
        'CallSession', 
        null=True, 
        blank=True, 
        on_delete=models.SET_NULL
    )
```

### Phase 2: WebSocket Signaling Infrastructure

#### 2.1 Extended WebSocket Consumer for Call Signaling
```python
# Extension to existing ChatConsumer in communications/consumers.py

class ChatConsumer(AsyncWebsocketConsumer):
    # ... existing methods ...
    
    async def receive(self, text_data):
        data = json.loads(text_data)
        message_type = data.get('type')
        
        # Existing message handlers...
        
        # New WebRTC signaling handlers
        if message_type == 'call_initiate':
            await self.handle_call_initiate(data)
        elif message_type == 'call_answer':
            await self.handle_call_answer(data)
        elif message_type == 'call_reject':
            await self.handle_call_reject(data)
        elif message_type == 'call_end':
            await self.handle_call_end(data)
        elif message_type == 'webrtc_offer':
            await self.handle_webrtc_offer(data)
        elif message_type == 'webrtc_answer':
            await self.handle_webrtc_answer(data)
        elif message_type == 'webrtc_ice_candidate':
            await self.handle_ice_candidate(data)
    
    async def handle_call_initiate(self, data):
        """Handle call initiation"""
        call_type = data.get('call_type', 'voice')
        callee_id = data.get('callee_id')
        
        # Create call session
        call_session = await self.create_call_session(callee_id, call_type)
        
        # Send call notification to callee
        await self.channel_layer.group_send(
            f'user_{callee_id}',
            {
                'type': 'incoming_call',
                'call_session_id': str(call_session.id),
                'caller_id': str(self.user.id),
                'caller_name': self.user.name,
                'call_type': call_type,
                'conversation_id': str(self.conversation_id)
            }
        )
    
    async def handle_webrtc_offer(self, data):
        """Handle WebRTC offer for peer connection"""
        await self.channel_layer.group_send(
            self.room_group_name,
            {
                'type': 'webrtc_offer',
                'offer': data['offer'],
                'sender_id': str(self.user.id),
                'call_session_id': data.get('call_session_id')
            }
        )
```

### Phase 3: Frontend WebRTC Implementation

#### 3.1 WebRTC Manager Class
```javascript
// New file: static/js/webrtc-manager.js

class WebRTCManager {
    constructor(communicationsClient) {
        this.communicationsClient = communicationsClient;
        this.peerConnection = null;
        this.localStream = null;
        this.remoteStream = null;
        this.currentCall = null;
        
        // ICE servers configuration
        this.iceServers = {
            iceServers: [
                { urls: 'stun:stun.l.google.com:19302' },
                { urls: 'stun:stun1.l.google.com:19302' },
                // TURN servers will be added for production
            ]
        };
        
        this.setupEventHandlers();
    }
    
    async initiateCall(userId, callType = 'voice') {
        try {
            // Get user media
            this.localStream = await this.getUserMedia(callType);
            
            // Create peer connection
            this.createPeerConnection();
            
            // Add local stream to peer connection
            this.localStream.getTracks().forEach(track => {
                this.peerConnection.addTrack(track, this.localStream);
            });
            
            // Send call initiation through WebSocket
            this.communicationsClient.sendMessage({
                type: 'call_initiate',
                callee_id: userId,
                call_type: callType
            });
            
            // Show outgoing call UI
            this.showOutgoingCallUI(userId, callType);
            
        } catch (error) {
            console.error('Error initiating call:', error);
            this.handleCallError(error);
        }
    }
    
    async answerCall(callSessionId) {
        try {
            // Get user media
            this.localStream = await this.getUserMedia(this.currentCall.type);
            
            // Create peer connection
            this.createPeerConnection();
            
            // Add local stream
            this.localStream.getTracks().forEach(track => {
                this.peerConnection.addTrack(track, this.localStream);
            });
            
            // Send answer through WebSocket
            this.communicationsClient.sendMessage({
                type: 'call_answer',
                call_session_id: callSessionId
            });
            
            // Show active call UI
            this.showActiveCallUI();
            
        } catch (error) {
            console.error('Error answering call:', error);
            this.rejectCall(callSessionId);
        }
    }
    
    createPeerConnection() {
        this.peerConnection = new RTCPeerConnection(this.iceServers);
        
        // Handle ICE candidates
        this.peerConnection.onicecandidate = (event) => {
            if (event.candidate) {
                this.communicationsClient.sendMessage({
                    type: 'webrtc_ice_candidate',
                    candidate: event.candidate,
                    call_session_id: this.currentCall?.id
                });
            }
        };
        
        // Handle remote stream
        this.peerConnection.ontrack = (event) => {
            this.remoteStream = event.streams[0];
            this.displayRemoteStream();
        };
        
        // Handle connection state changes
        this.peerConnection.onconnectionstatechange = () => {
            console.log('Connection state:', this.peerConnection.connectionState);
            if (this.peerConnection.connectionState === 'connected') {
                this.onCallConnected();
            } else if (this.peerConnection.connectionState === 'disconnected') {
                this.endCall();
            }
        };
    }
    
    async getUserMedia(callType) {
        const constraints = {
            audio: true,
            video: callType === 'video'
        };
        
        return await navigator.mediaDevices.getUserMedia(constraints);
    }
}
```

#### 3.2 Call UI Components
```javascript
// Extension to existing HeartGridCommunications class

class HeartGridCommunications {
    constructor() {
        // ... existing constructor code ...
        this.webrtcManager = new WebRTCManager(this);
        this.setupCallUI();
    }
    
    setupCallUI() {
        // Create call notification modal
        this.createCallNotificationModal();
        
        // Create active call interface
        this.createActiveCallInterface();
        
        // Add call buttons to chat interface
        this.addCallButtonsToChat();
    }
    
    createCallNotificationModal() {
        const modal = document.createElement('div');
        modal.id = 'call-notification-modal';
        modal.className = 'modal';
        modal.innerHTML = `
            <div class="modal-box">
                <h3 class="font-bold text-lg">Incoming Call</h3>
                <div class="py-4">
                    <div class="flex items-center space-x-4">
                        <div class="avatar">
                            <div class="w-16 rounded-full">
                                <img id="caller-avatar" src="" alt="Caller">
                            </div>
                        </div>
                        <div>
                            <p class="font-semibold" id="caller-name"></p>
                            <p class="text-sm opacity-70" id="call-type"></p>
                        </div>
                    </div>
                </div>
                <div class="modal-action">
                    <button class="btn btn-error" id="reject-call-btn">
                        <i class="fas fa-phone-slash"></i> Decline
                    </button>
                    <button class="btn btn-success" id="answer-call-btn">
                        <i class="fas fa-phone"></i> Answer
                    </button>
                </div>
            </div>
        `;
        document.body.appendChild(modal);
    }
    
    showIncomingCall(callData) {
        const modal = document.getElementById('call-notification-modal');
        document.getElementById('caller-name').textContent = callData.caller_name;
        document.getElementById('call-type').textContent = 
            callData.call_type === 'video' ? 'Video Call' : 'Voice Call';
        
        // Setup event handlers
        document.getElementById('answer-call-btn').onclick = () => {
            this.webrtcManager.answerCall(callData.call_session_id);
            modal.classList.remove('modal-open');
        };
        
        document.getElementById('reject-call-btn').onclick = () => {
            this.webrtcManager.rejectCall(callData.call_session_id);
            modal.classList.remove('modal-open');
        };
        
        modal.classList.add('modal-open');
    }
}
```

### Phase 4: STUN/TURN Server Configuration

#### 4.1 ICE Servers Setup
```python
# communications/webrtc_config.py

from django.conf import settings

class WebRTCConfig:
    @staticmethod
    def get_ice_servers():
        """Get ICE servers configuration for WebRTC"""
        ice_servers = [
            {'urls': 'stun:stun.l.google.com:19302'},
            {'urls': 'stun:stun1.l.google.com:19302'},
        ]
        
        # Add TURN servers for production
        if hasattr(settings, 'WEBRTC_TURN_SERVERS'):
            ice_servers.extend(settings.WEBRTC_TURN_SERVERS)
        
        return {'iceServers': ice_servers}

# settings.py additions
WEBRTC_TURN_SERVERS = [
    {
        'urls': 'turn:your-turn-server.com:3478',
        'username': 'your-username',
        'credential': 'your-password'
    }
]
```

### Phase 5: Integration with Existing Systems

#### 5.1 Presence System Integration
```python
# Extension to existing UserPresence model methods

class UserPresence(models.Model):
    # ... existing fields ...
    
    def set_in_call(self, call_session):
        """Set user as currently in a call"""
        self.call_availability = 'in_call'
        self.current_call_session = call_session
        self.save(update_fields=['call_availability', 'current_call_session'])
    
    def set_available_for_calls(self):
        """Set user as available for calls"""
        self.call_availability = 'available'
        self.current_call_session = None
        self.save(update_fields=['call_availability', 'current_call_session'])
    
    def can_receive_calls(self):
        """Check if user can receive calls"""
        return (
            self.status == 'online' and 
            self.call_availability == 'available' and
            not self.current_call_session
        )
```

#### 5.2 Notification System Extension
```python
# Extension to existing notifications.py

def notify_incoming_call(call_session):
    """Send incoming call notification"""
    notification_manager.send_call_notification(
        call_session.callee,
        'incoming_call',
        {
            'call_session_id': str(call_session.id),
            'caller_id': str(call_session.caller.id),
            'caller_name': call_session.caller.name,
            'call_type': call_session.call_type
        }
    )

def notify_call_ended(call_session, end_reason):
    """Send call ended notification"""
    participants = [call_session.caller, call_session.callee]
    for user in participants:
        notification_manager.send_call_notification(
            user,
            'call_ended',
            {
                'call_session_id': str(call_session.id),
                'end_reason': end_reason,
                'duration': str(call_session.duration) if call_session.duration else None
            }
        )
```

## 🔧 Technical Considerations

### Security & Privacy
- Media stream permissions handling
- Secure WebSocket connections (WSS)
- Call encryption through WebRTC's built-in DTLS
- User consent for camera/microphone access

### Performance Optimization
- Adaptive bitrate for video calls
- Audio/video codec selection
- Network quality monitoring
- Graceful degradation for poor connections

### Browser Compatibility
- WebRTC API support detection
- Fallback mechanisms for unsupported browsers
- Mobile browser considerations

### Production Requirements
- TURN server setup for NAT traversal
- Load balancing for WebSocket connections
- Call quality monitoring and analytics
- CDN for static assets

## 📱 Mobile Considerations

### Progressive Web App (PWA) Support
- Service worker for call notifications
- Background call handling
- Push notifications for missed calls

### Native App Integration
- WebView WebRTC support
- Native call UI integration
- Platform-specific optimizations

This implementation plan provides a comprehensive foundation for adding WebRTC calling functionality while leveraging your existing Django Channels infrastructure.
