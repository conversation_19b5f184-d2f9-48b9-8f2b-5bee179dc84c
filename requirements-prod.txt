# HeartGrid Dating Platform - Production Requirements
# Optimized for production deployment

# Include minimal requirements
-r requirements-minimal.txt

# Production Database
psycopg2-binary>=2.9.0

# Security & Performance
django-ratelimit>=4.1.0
asgiref>=3.7.0

# Monitoring & Logging
sentry-sdk>=1.38.0

# File Storage (for cloud deployment)
django-storages>=1.14.0
boto3>=1.34.0

# Caching
django-redis>=5.4.0

# Email Backend
django-ses>=3.5.0

# Health Checks
django-health-check>=3.17.0

# Background Job Monitoring
flower>=2.0.0

# Cryptography for payment processing
cryptography>=41.0.0
pycryptodome>=3.19.0

# Utilities
python-dateutil>=2.8.0
pytz>=2023.3
python-dotenv>=1.0.0
PyJWT>=2.8.0
