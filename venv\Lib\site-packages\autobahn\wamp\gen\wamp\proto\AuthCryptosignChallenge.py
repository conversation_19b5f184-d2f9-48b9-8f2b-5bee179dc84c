# automatically generated by the FlatBuffers compiler, do not modify

# namespace: proto

import flatbuffers
from flatbuffers.compat import import_numpy
np = import_numpy()

class AuthCryptosignChallenge(object):
    __slots__ = ['_tab']

    @classmethod
    def GetRootAs(cls, buf, offset=0):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, offset)
        x = AuthCryptosignChallenge()
        x.Init(buf, n + offset)
        return x

    @classmethod
    def GetRootAsAuthCryptosignChallenge(cls, buf, offset=0):
        """This method is deprecated. Please switch to GetRootAs."""
        return cls.GetRootAs(buf, offset)
    # AuthCryptosignChallenge
    def Init(self, buf, pos):
        self._tab = flatbuffers.table.Table(buf, pos)

    # AuthCryptosignChallenge
    def ChannelBinding(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Uint8Flags, o + self._tab.Pos)
        return 0

def AuthCryptosignChallengeStart(builder): builder.StartObject(1)
def Start(builder):
    return AuthCryptosignChallengeStart(builder)
def AuthCryptosignChallengeAddChannelBinding(builder, channelBinding): builder.PrependUint8Slot(0, channelBinding, 0)
def AddChannelBinding(builder, channelBinding):
    return AuthCryptosignChallengeAddChannelBinding(builder, channelBinding)
def AuthCryptosignChallengeEnd(builder): return builder.EndObject()
def End(builder):
    return AuthCryptosignChallengeEnd(builder)