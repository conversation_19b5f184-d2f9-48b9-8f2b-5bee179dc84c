{% extends "base.html" %}

{% block title %}User Management - HeartGrid Admin{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">User Management</h1>
            <p class="text-muted">Manage all HeartGrid users and their subscriptions</p>
        </div>
        <div>
            <a href="{{ url_for('admin_dashboard') }}" class="btn btn-secondary me-2">
                <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
            </a>
            <button class="btn btn-primary" onclick="refreshData()">
                <i class="fas fa-sync-alt me-2"></i>Refresh
            </button>
        </div>
    </div>

    <div class="card shadow">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">All Users</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover" id="usersTable">
                    <thead class="table-dark">
                        <tr>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Registration</th>
                            <th>Status</th>
                            <th>Subscription</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for user in users %}
                        <tr id="user-{{ user.id }}">
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="avatar-sm bg-primary rounded-circle d-flex align-items-center justify-content-center me-2">
                                        <span class="text-white fw-bold">{{ user.name[0].upper() }}</span>
                                    </div>
                                    {{ user.name }}
                                </div>
                            </td>
                            <td>{{ user.email }}</td>
                            <td>{{ user.created_at.strftime('%Y-%m-%d') }}</td>
                            <td>
                                {% if user.is_active %}
                                    <span class="badge bg-success">Active</span>
                                {% else %}
                                    <span class="badge bg-danger">Inactive</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if user.subscription %}
                                    {% if user.subscription.plan == 'trial' %}
                                        <span class="badge bg-warning">Trial</span>
                                    {% else %}
                                        <span class="badge bg-primary">{{ user.subscription.plan.title() }}</span>
                                    {% endif %}
                                    <small class="text-muted d-block">
                                        Expires: {{ user.subscription.expires_at.strftime('%Y-%m-%d') }}
                                    </small>
                                {% else %}
                                    <span class="badge bg-secondary">No Subscription</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-outline-primary" onclick="viewUser('{{ user.id }}')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-{% if user.is_active %}danger{% else %}success{% endif %}" 
                                            onclick="toggleUser('{{ user.id }}', {{ user.is_active|lower }})">
                                        <i class="fas fa-{% if user.is_active %}ban{% else %}check{% endif %}"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- User Details Modal -->
<div class="modal fade" id="userModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">User Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="userModalBody">
                <!-- User details will be loaded here -->
            </div>
        </div>
    </div>
</div>

<style>
.avatar-sm {
    width: 32px;
    height: 32px;
    font-size: 14px;
}

.table-hover tbody tr:hover {
    background-color: rgba(0,0,0,.075);
}
</style>

<script>
function toggleUser(userId, isActive) {
    const action = isActive ? 'deactivate' : 'activate';
    
    if (confirm(`Are you sure you want to ${action} this user?`)) {
        fetch(`/admin/toggle_user/${userId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification(data.message, 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                showNotification(data.error, 'error');
            }
        })
        .catch(error => {
            showNotification('An error occurred', 'error');
        });
    }
}

function viewUser(userId) {
    // Placeholder for user details view
    document.getElementById('userModalBody').innerHTML = `
        <div class="text-center">
            <p>User details for ID: ${userId}</p>
            <p class="text-muted">Detailed user information will be implemented here</p>
        </div>
    `;
    new bootstrap.Modal(document.getElementById('userModal')).show();
}

function refreshData() {
    showNotification('Refreshing user data...', 'info');
    setTimeout(() => location.reload(), 500);
}

// Initialize DataTable if available
document.addEventListener('DOMContentLoaded', function() {
    const table = document.getElementById('usersTable');
    if (table && typeof DataTable !== 'undefined') {
        new DataTable(table);
    }
});
</script>
{% endblock %}