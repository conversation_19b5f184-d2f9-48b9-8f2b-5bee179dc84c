Watching for file changes with StatReloader
Watching for file changes with StatReloader
Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\utils\deprecation.py", line 119, in __call__
    response = self.process_request(request)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\middleware\common.py", line 48, in process_request
    host = request.get_host()
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\http\request.py", line 191, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\utils\deprecation.py", line 119, in __call__
    response = self.process_request(request)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\middleware\common.py", line 48, in process_request
    host = request.get_host()
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\http\request.py", line 191, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid_django\settings.py changed, reloading.
Watching for file changes with StatReloader
Internal Server Error: /api/v1/auth/register/
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\views\generic\base.py", line 105, in view
    return self.dispatch(request, *args, **kwargs)
           ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\rest_framework\generics.py", line 194, in post
    return self.create(request, *args, **kwargs)
           ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\views.py", line 45, in create
    user = serializer.save()
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\rest_framework\serializers.py", line 210, in save
    self.instance = self.create(validated_data)
                    ~~~~~~~~~~~^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\serializers.py", line 50, in create
    user = User.objects.create_user(**validated_data)
TypeError: UserManager.create_user() missing 1 required positional argument: 'username'
Bad Request: /api/v1/auth/login/
C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\models.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\serializers.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\models.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\models.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\serializers.py changed, reloading.
Watching for file changes with StatReloader
Internal Server Error: /api/v1/auth/register/
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\views\generic\base.py", line 105, in view
    return self.dispatch(request, *args, **kwargs)
           ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\rest_framework\generics.py", line 194, in post
    return self.create(request, *args, **kwargs)
           ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\views.py", line 45, in create
    user = serializer.save()
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\rest_framework\serializers.py", line 210, in save
    self.instance = self.create(validated_data)
                    ~~~~~~~~~~~^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\serializers.py", line 62, in create
    Subscription.objects.create(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        user=user,
        ^^^^^^^^^^
    ...<3 lines>...
        features=['basic_chat']
        ^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\db\models\query.py", line 663, in create
    obj.save(force_insert=True, using=self.db)
    ~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\models.py", line 303, in save
    if self.expires_at and self.expires_at <= timezone.now() and self.status == 'active':
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: can't compare offset-naive and offset-aware datetimes
Internal Server Error: /api/v1/auth/login/
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\views\generic\base.py", line 105, in view
    return self.dispatch(request, *args, **kwargs)
           ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\rest_framework\decorators.py", line 50, in handler
    return func(*args, **kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\views.py", line 75, in user_login
    token, created = Token.objects.get_or_create(user=user)
                     ^^^^^^^^^^^^^
AttributeError: type object 'Token' has no attribute 'objects'
C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\serializers.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid_django\settings.py changed, reloading.
Watching for file changes with StatReloader
Bad Request: /api/v1/auth/register/
C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\views.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\urls.py changed, reloading.
Watching for file changes with StatReloader
Unauthorized: /api/v1/profiles/
Unauthorized: /api/v1/discover/
Unauthorized: /api/v1/subscription/
Unauthorized: /api/v1/notifications/
Unauthorized: /api/v1/stats/
C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid_django\settings.py changed, reloading.
Watching for file changes with StatReloader
Unauthorized: /api/v1/profiles/
Unauthorized: /api/v1/discover/
Unauthorized: /api/v1/subscription/
Unauthorized: /api/v1/notifications/
Unauthorized: /api/v1/stats/
Watching for file changes with StatReloader
C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\views.py changed, reloading.
C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\views.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\views.py changed, reloading.
C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\views.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\urls.py changed, reloading.
C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\urls.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\admin.py changed, reloading.
C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\admin.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\views.py changed, reloading.
C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\views.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\urls.py changed, reloading.
C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\urls.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Unauthorized: /api/v1/profiles/
Unauthorized: /api/v1/discover/
Unauthorized: /api/v1/subscription/
Unauthorized: /api/v1/notifications/
Unauthorized: /api/v1/stats/
Not Found: /api/v1/profile/
Not Found: /
Unauthorized: /api/v1/
Watching for file changes with StatReloader
Unauthorized: /api/v1/
"GET /api/v1/ HTTP/1.1" 401 58
"POST /api/v1/auth/register/ HTTP/1.1" 201 344
Not Found: /api/v1/profile/
"GET /api/v1/profile/ HTTP/1.1" 404 12754
"GET /api/v1/discover/ HTTP/1.1" 200 25
"GET /api/v1/matches/ HTTP/1.1" 200 14
"GET /api/v1/subscription/ HTTP/1.1" 200 495
Not Found: /api/v1/crypto/chains/
"GET /api/v1/crypto/chains/ HTTP/1.1" 404 12772
Not Found: /api/v1/gamification/stats/
"GET /api/v1/gamification/stats/ HTTP/1.1" 404 12787
Not Found: /api/v1/gamification/achievements/
"GET /api/v1/gamification/achievements/ HTTP/1.1" 404 12808
Not Found: /api/v1/gamification/leaderboard/
"GET /api/v1/gamification/leaderboard/ HTTP/1.1" 404 12805
Unauthorized: /api/v1/
"GET /api/v1/ HTTP/1.1" 401 58
Bad Request: /api/v1/auth/register/
"POST /api/v1/auth/register/ HTTP/1.1" 400 50
Unauthorized: /api/v1/
"GET /api/v1/ HTTP/1.1" 401 58
"POST /api/v1/auth/register/ HTTP/1.1" 201 354
"GET /api/v1/profiles/ HTTP/1.1" 200 496
"GET /api/v1/discover/ HTTP/1.1" 200 25
"GET /api/v1/matches/ HTTP/1.1" 200 14
"GET /api/v1/subscription/ HTTP/1.1" 200 505
"GET /api/v1/payment/chains/ HTTP/1.1" 200 803
"GET /api/v1/stats/ HTTP/1.1" 200 872
"GET /api/v1/achievements/ HTTP/1.1" 200 1597
"GET /api/v1/leaderboard/ HTTP/1.1" 200 350
Not Found: /
"GET / HTTP/1.1" 404 2953
Not Found: /favicon.ico
"GET /favicon.ico HTTP/1.1" 404 3004
C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\views.py changed, reloading.
C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\views.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Internal Server Error: /
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\views.py", line 42, in index
    return render(request, 'index.html')
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\backends\django.py", line 79, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\engine.py", line 177, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\engine.py", line 159, in find_template
    template = loader.get_template(name, skip=skip)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 196, in compile_nodelist
    nodelist = parser.parse()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loader_tags.py", line 299, in do_extends
    nodelist = parser.parse()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loader_tags.py", line 234, in do_block
    nodelist = parser.parse(("endblock",))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 489, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 487, in parse
    filter_expression = self.compile_filter(token.contents)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 609, in compile_filter
    return FilterExpression(token, self)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 710, in __init__
    raise TemplateSyntaxError(
    ...<2 lines>...
    )
django.template.exceptions.TemplateSyntaxError: Could not parse the remainder: '('register')' from 'url_for('register')'
"GET / HTTP/1.1" **********
- Broken pipe from ('127.0.0.1', 60600)
Watching for file changes with StatReloader
Internal Server Error: /
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\views.py", line 42, in index
    return render(request, 'index.html')
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loader_tags.py", line 134, in render
    compiled_parent = self.get_parent(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loader_tags.py", line 131, in get_parent
    return self.find_template(parent, context)
           ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loader_tags.py", line 109, in find_template
    template, origin = context.template.engine.find_template(
                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        template_name,
        ^^^^^^^^^^^^^^
        skip=history,
        ^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\engine.py", line 159, in find_template
    template = loader.get_template(name, skip=skip)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 196, in compile_nodelist
    nodelist = parser.parse()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 489, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 487, in parse
    filter_expression = self.compile_filter(token.contents)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 609, in compile_filter
    return FilterExpression(token, self)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 710, in __init__
    raise TemplateSyntaxError(
    ...<2 lines>...
    )
django.template.exceptions.TemplateSyntaxError: Could not parse the remainder: '('static', filename='css/style.css')' from 'url_for('static', filename='css/style.css')'
"GET / HTTP/1.1" **********
Watching for file changes with StatReloader
"GET / HTTP/1.1" 200 13984
"GET /static/css/style.css HTTP/1.1" 200 12612
"GET /static/js/app.js HTTP/1.1" 200 12477
"GET / HTTP/1.1" 200 13984
"GET / HTTP/1.1" 200 13984
"GET /static/css/style.css HTTP/1.1" 200 12612
"GET /static/js/app.js HTTP/1.1" 200 12477
Not Found: /favicon.ico
"GET /favicon.ico HTTP/1.1" 404 5928
Internal Server Error: /login/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\views.py", line 48, in login_page
    return render(request, 'login.html')
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\backends\django.py", line 79, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\engine.py", line 177, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\engine.py", line 159, in find_template
    template = loader.get_template(name, skip=skip)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 196, in compile_nodelist
    nodelist = parser.parse()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loader_tags.py", line 299, in do_extends
    nodelist = parser.parse()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loader_tags.py", line 234, in do_block
    nodelist = parser.parse(("endblock",))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 489, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 487, in parse
    filter_expression = self.compile_filter(token.contents)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 609, in compile_filter
    return FilterExpression(token, self)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 710, in __init__
    raise TemplateSyntaxError(
    ...<2 lines>...
    )
django.template.exceptions.TemplateSyntaxError: Could not parse the remainder: '('google_auth.google_login')' from 'url_for('google_auth.google_login')'
"GET /login/ HTTP/1.1" **********
"GET / HTTP/1.1" 200 13984
Internal Server Error: /login/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\views.py", line 48, in login_page
    return render(request, 'login.html')
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\backends\django.py", line 79, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\engine.py", line 177, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\engine.py", line 159, in find_template
    template = loader.get_template(name, skip=skip)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 196, in compile_nodelist
    nodelist = parser.parse()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loader_tags.py", line 299, in do_extends
    nodelist = parser.parse()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loader_tags.py", line 234, in do_block
    nodelist = parser.parse(("endblock",))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 489, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 487, in parse
    filter_expression = self.compile_filter(token.contents)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 609, in compile_filter
    return FilterExpression(token, self)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 710, in __init__
    raise TemplateSyntaxError(
    ...<2 lines>...
    )
django.template.exceptions.TemplateSyntaxError: Could not parse the remainder: '('google_auth.google_login')' from 'url_for('google_auth.google_login')'
"GET /login/ HTTP/1.1" **********
Internal Server Error: /register/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\views.py", line 54, in register_page
    return render(request, 'register.html')
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\backends\django.py", line 79, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\engine.py", line 177, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\engine.py", line 159, in find_template
    template = loader.get_template(name, skip=skip)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 196, in compile_nodelist
    nodelist = parser.parse()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loader_tags.py", line 299, in do_extends
    nodelist = parser.parse()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loader_tags.py", line 234, in do_block
    nodelist = parser.parse(("endblock",))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 489, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 487, in parse
    filter_expression = self.compile_filter(token.contents)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 609, in compile_filter
    return FilterExpression(token, self)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 710, in __init__
    raise TemplateSyntaxError(
    ...<2 lines>...
    )
django.template.exceptions.TemplateSyntaxError: Could not parse the remainder: '('login')' from 'url_for('login')'
"GET /register/ HTTP/1.1" **********
"GET /discover/ HTTP/1.1" 302 0
"GET /accounts/login/?next=/discover/ HTTP/1.1" 200 2361
"GET /profile/ HTTP/1.1" 302 0
"GET /accounts/login/?next=/profile/ HTTP/1.1" 200 2359
"GET /matches/ HTTP/1.1" 302 0
"GET /accounts/login/?next=/matches/ HTTP/1.1" 200 2359
"GET /chat/ HTTP/1.1" 302 0
"GET /accounts/login/?next=/chat/ HTTP/1.1" 200 2353
"GET /subscription/ HTTP/1.1" 302 0
"GET /accounts/login/?next=/subscription/ HTTP/1.1" 200 2369
"GET /notifications/ HTTP/1.1" 302 0
"GET /accounts/login/?next=/notifications/ HTTP/1.1" 200 2371
"GET /gamification/ HTTP/1.1" 302 0
"GET /accounts/login/?next=/gamification/ HTTP/1.1" 200 2369
"GET / HTTP/1.1" 200 13984
"GET /login/ HTTP/1.1" 200 10545
"GET /register/ HTTP/1.1" 200 11915
"GET /discover/ HTTP/1.1" 302 0
"GET /accounts/login/?next=/discover/ HTTP/1.1" 200 2361
"GET /profile/ HTTP/1.1" 302 0
"GET /accounts/login/?next=/profile/ HTTP/1.1" 200 2359
"GET /matches/ HTTP/1.1" 302 0
"GET /accounts/login/?next=/matches/ HTTP/1.1" 200 2359
"GET /chat/ HTTP/1.1" 302 0
"GET /accounts/login/?next=/chat/ HTTP/1.1" 200 2353
"GET /subscription/ HTTP/1.1" 302 0
"GET /accounts/login/?next=/subscription/ HTTP/1.1" 200 2369
"GET /notifications/ HTTP/1.1" 302 0
"GET /accounts/login/?next=/notifications/ HTTP/1.1" 200 2371
"GET /gamification/ HTTP/1.1" 302 0
"GET /accounts/login/?next=/gamification/ HTTP/1.1" 200 2369
"GET /login/ HTTP/1.1" 200 10545
"GET /register/ HTTP/1.1" 200 11915
"GET / HTTP/1.1" 200 13984
"GET /login/ HTTP/1.1" 200 10545
"GET /register/ HTTP/1.1" 200 11915
"GET /discover/ HTTP/1.1" 302 0
"GET /accounts/login/?next=/discover/ HTTP/1.1" 200 2361
"GET /profile/ HTTP/1.1" 302 0
"GET /accounts/login/?next=/profile/ HTTP/1.1" 200 2359
"GET /matches/ HTTP/1.1" 302 0
"GET /accounts/login/?next=/matches/ HTTP/1.1" 200 2359
"GET /chat/ HTTP/1.1" 302 0
"GET /accounts/login/?next=/chat/ HTTP/1.1" 200 2353
"GET /subscription/ HTTP/1.1" 302 0
"GET /accounts/login/?next=/subscription/ HTTP/1.1" 200 2369
"GET /notifications/ HTTP/1.1" 302 0
"GET /accounts/login/?next=/notifications/ HTTP/1.1" 200 2371
"GET /gamification/ HTTP/1.1" 302 0
"GET /accounts/login/?next=/gamification/ HTTP/1.1" 200 2369
"GET /static/css/style.css HTTP/1.1" 200 12612
"GET /static/js/app.js HTTP/1.1" 200 12477
C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\views.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET / HTTP/1.1" 200 14088
"GET /login/ HTTP/1.1" 200 10791
"GET /register/ HTTP/1.1" 200 12161
"GET /discover/ HTTP/1.1" 302 0
"GET /static/css/style.css HTTP/1.1" 200 12612
"GET /static/js/app.js HTTP/1.1" 200 14943
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET /subscription/ HTTP/1.1" 302 0
Watching for file changes with StatReloader
"GET /accounts/login/?next=/subscription/ HTTP/1.1" 200 2369
Watching for file changes with StatReloader
"GET / HTTP/1.1" 200 14088
"GET /static/js/app.js HTTP/1.1" 200 14943
"GET /static/css/style.css HTTP/1.1" 200 12612
"GET /login/ HTTP/1.1" 200 10791
Not Found: /favicon.ico
"GET /favicon.ico HTTP/1.1" 404 6188
Not Found: /favicon.ico
"GET /favicon.ico HTTP/1.1" 404 6188
"GET / HTTP/1.1" 200 14088
"GET /static/css/style.css HTTP/1.1" 200 12612
"GET /static/js/app.js HTTP/1.1" 200 14943
Not Found: /favicon.ico
"GET /favicon.ico HTTP/1.1" 404 6188
"GET /register/ HTTP/1.1" 200 12161
"GET /login/ HTTP/1.1" 200 10791
"POST /login/ HTTP/1.1" 200 11128
"GET /register/ HTTP/1.1" 200 12161
Internal Server Error: /register/
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\utils\decorators.py", line 192, in _view_wrapper
    result = _process_exception(request, e)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\utils\decorators.py", line 190, in _view_wrapper
    response = view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\views.py", line 101, in register_page
    if User.objects.filter(email=email).exists():
       ^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\venv\Lib\site-packages\django\db\models\manager.py", line 196, in __get__
    raise AttributeError(
    ...<5 lines>...
    )
AttributeError: Manager isn't available; 'auth.User' has been swapped for 'heartgrid.User'
"POST /register/ HTTP/1.1" 500 87789
C:\Users\<USER>\Documents\augment-projects\HeartGridPlatform\heartgrid\views.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET /register/ HTTP/1.1" 200 12161
"GET /static/css/style.css HTTP/1.1" 304 0
"GET /static/js/app.js HTTP/1.1" 200 14943
Not Found: /favicon.ico
"GET /favicon.ico HTTP/1.1" 404 6188
Not Found: /api/register/
Not Found: /api/profiles/
Not Found: /api/matches/
Not Found: /api/messages/
Not Found: /dashboard/
Not Found: /messages/
Not Found: /dashboard/
Not Found: /messages/
Not Found: /api/profiles/
Not Found: /api/matches/
Not Found: /api/messages/
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
