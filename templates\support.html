{% extends "base.html" %}

{% block title %}Support - HeartGrid{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="text-center mb-5">
                <h1 class="display-5 fw-bold text-primary">Support Center</h1>
                <p class="lead text-muted">We're here to help you find love</p>
            </div>

            <!-- Support Status -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h5 class="mb-1">Your Support Level</h5>
                            <p class="text-muted mb-0">{{ support_info.support_level }}</p>
                        </div>
                        <div class="col-md-4 text-end">
                            {% if support_info.has_priority %}
                            <span class="badge bg-success fs-6">Priority Support</span>
                            {% else %}
                            <span class="badge bg-secondary fs-6">Standard Support</span>
                            {% endif %}
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <small class="text-muted">Response Time</small>
                            <p class="fw-bold mb-0">{{ support_info.response_time }}</p>
                        </div>
                        {% if support_info.dedicated_support %}
                        <div class="col-md-6">
                            <small class="text-muted">Dedicated Support</small>
                            <p class="fw-bold mb-0">Available</p>
                        </div>
                        {% endif %}
                    </div>
                    
                    {% if not support_info.has_priority %}
                    <div class="mt-3">
                        <small class="text-muted">
                            Upgrade to premium for priority support with faster response times and dedicated assistance.
                        </small>
                        <a href="{{ url_for('subscription') }}" class="btn btn-primary btn-sm ms-2">Upgrade</a>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Contact Methods -->
            <div class="row g-4 mb-5">
                <div class="col-md-6">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-body text-center">
                            <div class="support-icon mb-3">
                                <i class="fas fa-envelope fa-2x text-primary"></i>
                            </div>
                            <h5>Email Support</h5>
                            <p class="text-muted">Get help via email</p>
                            <p class="fw-bold"><EMAIL></p>
                            <button class="btn btn-outline-primary" onclick="openEmailSupport()">
                                Send Email
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-body text-center">
                            <div class="support-icon mb-3">
                                <i class="fas fa-comments fa-2x text-success"></i>
                            </div>
                            <h5>Live Chat</h5>
                            <p class="text-muted">Chat with our support team</p>
                            <p class="fw-bold">Available 9 AM - 6 PM</p>
                            <button class="btn btn-outline-success" onclick="openLiveChat()" 
                                    {{ 'disabled' if not support_info.has_priority }}>
                                {% if support_info.has_priority %}
                                Start Chat
                                {% else %}
                                Premium Only
                                {% endif %}
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- FAQ Section -->
            <div class="card border-0 shadow-sm">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-question-circle me-2"></i>Frequently Asked Questions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="accordion" id="supportFAQ">
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#faq1">
                                    How do I report inappropriate behavior?
                                </button>
                            </h2>
                            <div id="faq1" class="accordion-collapse collapse show" data-bs-parent="#supportFAQ">
                                <div class="accordion-body">
                                    You can report users by going to their profile and clicking the "Report" button. We take all reports seriously and will investigate within 24 hours.
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq2">
                                    How do I cancel my subscription?
                                </button>
                            </h2>
                            <div id="faq2" class="accordion-collapse collapse" data-bs-parent="#supportFAQ">
                                <div class="accordion-body">
                                    Go to your subscription settings and click "Cancel Subscription". Your premium features will remain active until the end of your current billing period.
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq3">
                                    Why can't I see my matches?
                                </button>
                            </h2>
                            <div id="faq3" class="accordion-collapse collapse" data-bs-parent="#supportFAQ">
                                <div class="accordion-body">
                                    Make sure your profile is complete with photos and bio. Also check your discovery settings and age/distance preferences.
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq4">
                                    How do video calls work?
                                </button>
                            </h2>
                            <div id="faq4" class="accordion-collapse collapse" data-bs-parent="#supportFAQ">
                                <div class="accordion-body">
                                    Video calls are available for premium subscribers. Both users must have premium subscriptions and be matched to start a call.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.support-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: rgba(13, 110, 253, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
}
</style>

<script>
function openEmailSupport() {
    window.location.href = 'mailto:<EMAIL>?subject=HeartGrid Support Request';
}

function openLiveChat() {
    {% if support_info.has_priority %}
    showNotification('Live chat feature coming soon!', 'info');
    {% else %}
    showNotification('Live chat requires premium subscription!', 'warning');
    {% endif %}
}
</script>
{% endblock %}