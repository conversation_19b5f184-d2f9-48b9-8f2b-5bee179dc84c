#!/usr/bin/env python
"""
Focused functionality test for HeartGrid Communications features
Tests core messaging and WebRTC calling functionality
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'heartgrid_django.settings')
django.setup()

from django.contrib.auth import get_user_model
from communications.models import (
    Conversation, Message, MessageStatus, UserPresence, 
    CallSession, UserCallSettings
)
from communications.webrtc_config import WebRTCConfig
from communications.notifications import NotificationManager

User = get_user_model()


def test_models_functionality():
    """Test core model functionality"""
    print("🧪 Testing Models Functionality...")
    
    results = {'passed': 0, 'failed': 0, 'errors': []}
    
    try:
        # Test User model
        print("  Testing User model...")
        user_count = User.objects.count()
        print(f"    ✓ User model accessible - {user_count} users in database")
        results['passed'] += 1
        
        # Test Conversation model
        print("  Testing Conversation model...")
        conv_count = Conversation.objects.count()
        print(f"    ✓ Conversation model accessible - {conv_count} conversations in database")
        results['passed'] += 1
        
        # Test Message model
        print("  Testing Message model...")
        msg_count = Message.objects.count()
        print(f"    ✓ Message model accessible - {msg_count} messages in database")
        results['passed'] += 1
        
        # Test UserPresence model
        print("  Testing UserPresence model...")
        presence_count = UserPresence.objects.count()
        print(f"    ✓ UserPresence model accessible - {presence_count} presence records in database")
        results['passed'] += 1
        
        # Test CallSession model
        print("  Testing CallSession model...")
        call_count = CallSession.objects.count()
        print(f"    ✓ CallSession model accessible - {call_count} call sessions in database")
        results['passed'] += 1
        
        # Test UserCallSettings model
        print("  Testing UserCallSettings model...")
        settings_count = UserCallSettings.objects.count()
        print(f"    ✓ UserCallSettings model accessible - {settings_count} call settings in database")
        results['passed'] += 1
        
    except Exception as e:
        print(f"    ❌ Model test failed: {e}")
        results['failed'] += 1
        results['errors'].append(f"Model test: {e}")
    
    return results


def test_webrtc_configuration():
    """Test WebRTC configuration"""
    print("\n🌐 Testing WebRTC Configuration...")
    
    results = {'passed': 0, 'failed': 0, 'errors': []}
    
    try:
        # Test configuration retrieval
        print("  Testing configuration retrieval...")
        config = WebRTCConfig.get_webrtc_configuration()
        if config and 'iceServers' in config:
            print(f"    ✓ WebRTC configuration retrieved - {len(config['iceServers'])} ICE servers")
            results['passed'] += 1
        else:
            print("    ❌ WebRTC configuration invalid")
            results['failed'] += 1
            results['errors'].append("WebRTC configuration invalid")
        
        # Test ICE servers
        print("  Testing ICE servers...")
        ice_servers = config.get('iceServers', [])
        stun_servers = [s for s in ice_servers if s['urls'].startswith('stun:')]
        if len(stun_servers) >= 5:
            print(f"    ✓ STUN servers configured - {len(stun_servers)} servers")
            results['passed'] += 1
        else:
            print(f"    ❌ Insufficient STUN servers - only {len(stun_servers)} configured")
            results['failed'] += 1
            results['errors'].append(f"Only {len(stun_servers)} STUN servers configured")
        
        # Test configuration validation
        print("  Testing configuration validation...")
        is_valid, errors = WebRTCConfig.validate_configuration()
        if is_valid:
            print("    ✓ Configuration validation passed")
            results['passed'] += 1
        else:
            print(f"    ❌ Configuration validation failed: {'; '.join(errors)}")
            results['failed'] += 1
            results['errors'].append(f"Configuration validation: {'; '.join(errors)}")
        
        # Test media constraints
        print("  Testing media constraints...")
        constraints = WebRTCConfig.get_media_constraints()
        if 'audio' in constraints and 'video' in constraints:
            print("    ✓ Media constraints configured")
            results['passed'] += 1
        else:
            print("    ❌ Media constraints missing")
            results['failed'] += 1
            results['errors'].append("Media constraints missing")
        
    except Exception as e:
        print(f"    ❌ WebRTC configuration test failed: {e}")
        results['failed'] += 1
        results['errors'].append(f"WebRTC configuration: {e}")
    
    return results


def test_notification_system():
    """Test notification system"""
    print("\n🔔 Testing Notification System...")
    
    results = {'passed': 0, 'failed': 0, 'errors': []}
    
    try:
        # Test notification manager instantiation
        print("  Testing NotificationManager...")
        notification_manager = NotificationManager()
        if notification_manager:
            print("    ✓ NotificationManager instantiated successfully")
            results['passed'] += 1
        else:
            print("    ❌ NotificationManager instantiation failed")
            results['failed'] += 1
            results['errors'].append("NotificationManager instantiation failed")
        
        # Test notification methods exist
        print("  Testing notification methods...")
        required_methods = [
            'send_message_notification',
            'send_presence_notification',
            'send_call_notification',
            'send_call_availability_notification'
        ]
        
        missing_methods = []
        for method in required_methods:
            if not hasattr(notification_manager, method):
                missing_methods.append(method)
        
        if not missing_methods:
            print(f"    ✓ All {len(required_methods)} notification methods available")
            results['passed'] += 1
        else:
            print(f"    ❌ Missing notification methods: {', '.join(missing_methods)}")
            results['failed'] += 1
            results['errors'].append(f"Missing methods: {', '.join(missing_methods)}")
        
    except Exception as e:
        print(f"    ❌ Notification system test failed: {e}")
        results['failed'] += 1
        results['errors'].append(f"Notification system: {e}")
    
    return results


def test_file_structure():
    """Test file structure"""
    print("\n📁 Testing File Structure...")
    
    results = {'passed': 0, 'failed': 0, 'errors': []}
    
    required_files = [
        'communications/models.py',
        'communications/consumers.py',
        'communications/notifications.py',
        'communications/webrtc_config.py',
        'static/js/webrtc-manager.js',
        'static/js/communications.js',
        'static/css/webrtc-calling.css',
        'communications/management/commands/test_webrtc_config.py'
    ]
    
    missing_files = []
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"    ✓ {file_path}")
        else:
            print(f"    ❌ {file_path}")
            missing_files.append(file_path)
    
    if not missing_files:
        print(f"    ✓ All {len(required_files)} required files present")
        results['passed'] += 1
    else:
        print(f"    ❌ Missing files: {len(missing_files)}")
        results['failed'] += 1
        results['errors'].append(f"Missing files: {', '.join(missing_files)}")
    
    return results


def test_javascript_syntax():
    """Test JavaScript file syntax"""
    print("\n🔧 Testing JavaScript Syntax...")
    
    results = {'passed': 0, 'failed': 0, 'errors': []}
    
    js_files = [
        'static/js/webrtc-manager.js',
        'static/js/communications.js'
    ]
    
    for js_file in js_files:
        try:
            if os.path.exists(js_file):
                with open(js_file, 'r') as f:
                    content = f.read()
                
                # Basic syntax checks
                balanced_braces = content.count('{') == content.count('}')
                balanced_parens = content.count('(') == content.count(')')
                has_classes = 'class ' in content or 'function ' in content
                
                if balanced_braces and balanced_parens and has_classes:
                    print(f"    ✓ {js_file} - syntax OK")
                    results['passed'] += 1
                else:
                    print(f"    ❌ {js_file} - syntax issues")
                    results['failed'] += 1
                    results['errors'].append(f"{js_file} syntax issues")
            else:
                print(f"    ❌ {js_file} - file not found")
                results['failed'] += 1
                results['errors'].append(f"{js_file} not found")
        
        except Exception as e:
            print(f"    ❌ {js_file} - error: {e}")
            results['failed'] += 1
            results['errors'].append(f"{js_file}: {e}")
    
    return results


def main():
    """Main test runner"""
    print("🚀 HeartGrid Communications Functionality Test")
    print("=" * 60)
    
    all_results = {'passed': 0, 'failed': 0, 'errors': []}
    
    # Run all tests
    test_functions = [
        test_models_functionality,
        test_webrtc_configuration,
        test_notification_system,
        test_file_structure,
        test_javascript_syntax
    ]
    
    for test_func in test_functions:
        result = test_func()
        all_results['passed'] += result['passed']
        all_results['failed'] += result['failed']
        all_results['errors'].extend(result['errors'])
    
    # Print summary
    print("\n" + "=" * 60)
    print("🏁 Test Summary")
    print("=" * 60)
    print(f"✅ Passed: {all_results['passed']}")
    print(f"❌ Failed: {all_results['failed']}")
    
    if all_results['errors']:
        print("\n🔍 Error Details:")
        for error in all_results['errors']:
            print(f"   • {error}")
    
    total_tests = all_results['passed'] + all_results['failed']
    success_rate = (all_results['passed'] / total_tests) * 100 if total_tests > 0 else 0
    print(f"\n📊 Success Rate: {success_rate:.1f}%")
    
    if success_rate >= 95:
        print("🎉 Excellent! All communication features are working perfectly.")
        return True
    elif success_rate >= 85:
        print("👍 Good! Minor issues found but core functionality works.")
        return True
    elif success_rate >= 70:
        print("⚠️ Some issues found. Review and fix before production.")
        return False
    else:
        print("💥 Significant issues found. Major fixes needed.")
        return False


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
