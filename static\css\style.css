/* HeartGrid Custom Styles */

:root {
    --primary-color: #e91e63;
    --secondary-color: #ff4081;
    --accent-color: #f50057;
    --love-pink: #fce4ec;
    --warm-red: #d32f2f;
    --soft-gray: #f5f5f5;
    --text-dark: #333;
    --text-light: #666;
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-hover: 0 8px 25px rgba(0, 0, 0, 0.15);
    --border-radius: 12px;
}

/* Global Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #fce4ec 0%, #f8bbd9 50%, #e1bee7 100%);
    min-height: 100vh;
    color: var(--text-dark);
}

/* Override Bootstrap primary color */
.btn-primary {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    border: none;
    border-radius: var(--border-radius);
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: linear-gradient(45deg, var(--accent-color), var(--primary-color));
    transform: translateY(-2px);
    box-shadow: var(--shadow-hover);
}

.text-primary {
    color: var(--primary-color) !important;
}

/* Animations */
@keyframes heartbeat {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.heartbeat {
    animation: heartbeat 1.5s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.floating-hearts {
    position: absolute;
    top: 10%;
    right: 10%;
    z-index: 1;
}

.floating-hearts i {
    position: absolute;
    font-size: 1.5rem;
    animation: float 3s ease-in-out infinite;
}

.floating-hearts i:nth-child(1) { animation-delay: 0s; top: 0; right: 0; }
.floating-hearts i:nth-child(2) { animation-delay: 0.5s; top: 30px; right: 40px; }
.floating-hearts i:nth-child(3) { animation-delay: 1s; top: 60px; right: 20px; }
.floating-hearts i:nth-child(4) { animation-delay: 1.5s; top: 90px; right: 60px; }
.floating-hearts i:nth-child(5) { animation-delay: 2s; top: 120px; right: 10px; }

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, rgba(252, 228, 236, 0.8), rgba(248, 187, 217, 0.8));
    min-height: 100vh;
    position: relative;
    overflow: hidden;
}

.hero-content h1 {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-image {
    position: relative;
}

.hero-image img {
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-hover);
}

/* Feature Cards */
.feature-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
    border: none;
    height: 100%;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-hover);
}

.feature-icon {
    background: var(--love-pink);
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
}

/* Story Cards */
.story-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
    transition: all 0.3s ease;
}

.story-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-hover);
}

.story-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.story-content {
    padding: 1.5rem;
}

.story-hearts {
    margin-top: 1rem;
}

/* Auth Pages */
.auth-card {
    background: white;
    padding: 2.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-hover);
    border: none;
}

.auth-image {
    position: relative;
    height: 100%;
    min-height: 400px;
}

.auth-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: var(--border-radius);
}

.auth-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
    padding: 2rem;
    border-radius: 0 0 var(--border-radius) var(--border-radius);
}

/* Profile Page */
.profile-header {
    background: white;
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    margin-bottom: 2rem;
}

.photo-item {
    position: relative;
    aspect-ratio: 1;
    overflow: hidden;
    border-radius: var(--border-radius);
}

.photo-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.photo-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.photo-item:hover .photo-overlay {
    opacity: 1;
}

.empty-photos {
    background: var(--soft-gray);
    border-radius: var(--border-radius);
    border: 2px dashed #ddd;
}

/* Discover Page */
.discover-header {
    background: white;
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    margin-bottom: 2rem;
}

.profiles-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.profile-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
    transition: all 0.3s ease;
    cursor: pointer;
}

.profile-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-hover);
}

.profile-card.liking {
    animation: pulse 0.5s ease-in-out;
    border: 3px solid var(--primary-color);
}

.profile-card.liked {
    opacity: 0;
    transform: translateX(100px) rotate(15deg);
}

.profile-card.passed {
    opacity: 0;
    transform: translateX(-100px) rotate(-15deg);
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.profile-image {
    position: relative;
    aspect-ratio: 4/5;
    overflow: hidden;
}

.profile-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.no-photo {
    width: 100%;
    height: 100%;
    background: var(--soft-gray);
    display: flex;
    align-items: center;
    justify-content: center;
}

.profile-actions {
    position: absolute;
    bottom: 1rem;
    right: 1rem;
    display: flex;
    gap: 0.5rem;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.profile-card:hover .profile-actions {
    opacity: 1;
}

.profile-actions .btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow);
}

.like-btn {
    background: var(--primary-color) !important;
    border: none;
    color: white;
}

.like-btn:hover {
    background: var(--accent-color) !important;
    transform: scale(1.1);
}

.pass-btn:hover {
    background: #dc3545 !important;
    color: white;
    transform: scale(1.1);
}

.profile-info {
    padding: 1.5rem;
}

.profile-name {
    color: var(--text-dark);
    font-weight: 600;
}

.profile-location {
    font-size: 0.9rem;
}

.profile-bio {
    line-height: 1.4;
    margin-bottom: 1rem;
}

.profile-interests {
    margin-top: 1rem;
}

.profile-interests .badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border: 1px solid #ddd;
}

/* Match Modal */
.match-modal .modal-content {
    border: none;
    border-radius: var(--border-radius);
    background: linear-gradient(135deg, white, var(--love-pink));
}

.match-animation {
    animation: heartbeat 1s ease-in-out infinite;
}

/* Matches Page */
.matches-header {
    background: white;
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    margin-bottom: 2rem;
}

.matches-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1.5rem;
}

.match-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
    transition: all 0.3s ease;
}

.match-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-hover);
}

.match-image {
    position: relative;
    aspect-ratio: 1;
    overflow: hidden;
}

.match-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.match-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    width: 30px;
    height: 30px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: heartbeat 2s ease-in-out infinite;
}

.match-info {
    padding: 1.5rem;
}

.match-name {
    color: var(--text-dark);
    font-weight: 600;
}

/* Chat Page */
.chat-container {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
    height: 80vh;
    display: flex;
    flex-direction: column;
}

.chat-header {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 1rem;
    border-bottom: 1px solid #eee;
}

.chat-avatar {
    width: 40px;
    height: 40px;
}

.chat-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.avatar-placeholder {
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.chat-messages {
    flex: 1;
    padding: 1rem;
    overflow-y: auto;
    background: var(--soft-gray);
}

.message {
    margin-bottom: 1rem;
}

.own-message {
    text-align: right;
}

.other-message {
    text-align: left;
}

.message-content {
    display: inline-block;
    max-width: 70%;
    padding: 0.75rem 1rem;
    border-radius: 1rem;
    word-wrap: break-word;
}

.own-message .message-content {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    color: white;
}

.other-message .message-content {
    background: white;
    color: var(--text-dark);
    box-shadow: var(--shadow);
}

.message-time {
    font-size: 0.75rem;
    color: var(--text-light);
    margin-top: 0.25rem;
}

.chat-starter {
    text-align: center;
    padding: 2rem;
    color: var(--text-light);
}

.chat-input {
    padding: 1rem;
    border-top: 1px solid #eee;
    background: white;
}

.chat-input .form-control {
    border-radius: 2rem;
    border: 1px solid #ddd;
}

.chat-input .btn {
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Empty State */
.empty-state {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    margin: 2rem 0;
}

/* Cards */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

.card-header {
    background: var(--love-pink);
    border-bottom: 1px solid #ddd;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
    font-weight: 600;
}

/* Form Styles */
.form-control, .form-select {
    border-radius: var(--border-radius);
    border: 1px solid #ddd;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(233, 30, 99, 0.25);
}

.input-group-text {
    background: var(--love-pink);
    border-color: #ddd;
    color: var(--primary-color);
}

/* Responsive Design */
@media (max-width: 768px) {
    .profiles-grid {
        grid-template-columns: 1fr;
    }
    
    .matches-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    }
    
    .hero-content h1 {
        font-size: 2rem;
    }
    
    .chat-container {
        height: 70vh;
    }
    
    .auth-card {
        padding: 1.5rem;
    }
}

@media (max-width: 576px) {
    .container {
        padding-left: 1rem;
        padding-right: 1rem;
    }
    
    .profile-card, .match-card {
        margin-bottom: 1rem;
    }
    
    .floating-hearts {
        display: none;
    }
}

/* Navigation */
.navbar {
    box-shadow: var(--shadow);
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
}

.nav-link {
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-link:hover {
    color: var(--primary-color) !important;
}

/* Footer */
footer {
    border-top: 1px solid #eee;
    margin-top: auto;
}

/* Utility Classes */
.rounded-lg {
    border-radius: var(--border-radius) !important;
}

.shadow-custom {
    box-shadow: var(--shadow);
}

.shadow-hover-custom {
    box-shadow: var(--shadow-hover);
}
