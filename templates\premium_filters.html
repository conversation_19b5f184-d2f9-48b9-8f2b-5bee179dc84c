{% extends "base.html" %}

{% block title %}Premium Search - HeartGrid{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">Advanced Search</h1>
                    <p class="text-muted">Find exactly what you're looking for</p>
                </div>
                <a href="{{ url_for('discover') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Discovery
                </a>
            </div>

            {% if not has_premium_filters %}
            <div class="alert alert-warning">
                <h5><i class="fas fa-lock me-2"></i>Premium Feature</h5>
                <p class="mb-2">Advanced search filters require a premium subscription.</p>
                <a href="{{ url_for('subscription') }}" class="btn btn-primary btn-sm">
                    Upgrade Now
                </a>
            </div>
            {% endif %}

            <div class="card border-0 shadow-sm">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-filter me-2"></i>Search Filters
                    </h5>
                </div>
                <div class="card-body">
                    <form id="advancedSearchForm">
                        <div class="row g-3">
                            <!-- Age Range -->
                            <div class="col-md-6">
                                <label class="form-label fw-bold">Age Range</label>
                                <div class="d-flex align-items-center">
                                    <input type="range" class="form-range me-3" id="minAge" min="18" max="65" value="25" 
                                           {{ 'disabled' if not has_premium_filters }}>
                                    <span id="minAgeValue">25</span>
                                    <span class="mx-2">-</span>
                                    <input type="range" class="form-range me-3" id="maxAge" min="18" max="65" value="35"
                                           {{ 'disabled' if not has_premium_filters }}>
                                    <span id="maxAgeValue">35</span>
                                </div>
                            </div>

                            <!-- Distance -->
                            <div class="col-md-6">
                                <label class="form-label fw-bold">Distance (km)</label>
                                <div class="d-flex align-items-center">
                                    <input type="range" class="form-range me-3" id="distance" min="5" max="{{ max_radius }}" value="{{ max_radius }}"
                                           {{ 'disabled' if not has_premium_filters }}>
                                    <span id="distanceValue">{{ max_radius }}</span> km
                                </div>
                                {% if not has_premium_filters %}
                                <small class="text-muted">Free users: {{ max_radius }}km max</small>
                                {% endif %}
                            </div>

                            <!-- Education Level -->
                            <div class="col-md-6">
                                <label class="form-label fw-bold">Education Level</label>
                                <select class="form-select" {{ 'disabled' if not has_premium_filters }}>
                                    <option value="">Any Education</option>
                                    <option value="high_school">High School</option>
                                    <option value="some_college">Some College</option>
                                    <option value="bachelors">Bachelor's Degree</option>
                                    <option value="masters">Master's Degree</option>
                                    <option value="phd">PhD</option>
                                </select>
                            </div>

                            <!-- Occupation -->
                            <div class="col-md-6">
                                <label class="form-label fw-bold">Occupation</label>
                                <select class="form-select" {{ 'disabled' if not has_premium_filters }}>
                                    <option value="">Any Occupation</option>
                                    <option value="healthcare">Healthcare</option>
                                    <option value="education">Education</option>
                                    <option value="technology">Technology</option>
                                    <option value="business">Business</option>
                                    <option value="creative">Creative Arts</option>
                                    <option value="legal">Legal</option>
                                    <option value="other">Other</option>
                                </select>
                            </div>

                            <!-- Relationship Goals -->
                            <div class="col-md-6">
                                <label class="form-label fw-bold">Relationship Goals</label>
                                <select class="form-select" {{ 'disabled' if not has_premium_filters }}>
                                    <option value="">Any Goals</option>
                                    <option value="casual">Casual Dating</option>
                                    <option value="serious">Serious Relationship</option>
                                    <option value="marriage">Marriage</option>
                                    <option value="friendship">Friendship</option>
                                </select>
                            </div>

                            <!-- Lifestyle -->
                            <div class="col-md-6">
                                <label class="form-label fw-bold">Lifestyle</label>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="nonsmoker" 
                                           {{ 'disabled' if not has_premium_filters }}>
                                    <label class="form-check-label" for="nonsmoker">Non-smoker</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="fitness" 
                                           {{ 'disabled' if not has_premium_filters }}>
                                    <label class="form-check-label" for="fitness">Fitness Enthusiast</label>
                                </div>
                            </div>

                            <!-- Height Range -->
                            <div class="col-md-6">
                                <label class="form-label fw-bold">Height Range (cm)</label>
                                <div class="d-flex align-items-center">
                                    <input type="range" class="form-range me-3" id="minHeight" min="150" max="200" value="160"
                                           {{ 'disabled' if not has_premium_filters }}>
                                    <span id="minHeightValue">160</span>
                                    <span class="mx-2">-</span>
                                    <input type="range" class="form-range me-3" id="maxHeight" min="150" max="200" value="180"
                                           {{ 'disabled' if not has_premium_filters }}>
                                    <span id="maxHeightValue">180</span> cm
                                </div>
                            </div>

                            <!-- Languages -->
                            <div class="col-md-6">
                                <label class="form-label fw-bold">Languages</label>
                                <select class="form-select" multiple {{ 'disabled' if not has_premium_filters }}>
                                    <option value="english">English</option>
                                    <option value="spanish">Spanish</option>
                                    <option value="french">French</option>
                                    <option value="german">German</option>
                                    <option value="chinese">Chinese</option>
                                    <option value="other">Other</option>
                                </select>
                                <small class="text-muted">Hold Ctrl/Cmd to select multiple</small>
                            </div>
                        </div>

                        <div class="mt-4 text-center">
                            <button type="submit" class="btn btn-primary btn-lg" 
                                    {{ 'disabled' if not has_premium_filters }}>
                                <i class="fas fa-search me-2"></i>Search with Filters
                            </button>
                            <button type="button" class="btn btn-outline-secondary ms-2" onclick="resetFilters()">
                                Reset Filters
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            {% if has_premium_filters %}
            <div class="card border-0 shadow-sm mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-users me-2"></i>Search Results
                    </h5>
                </div>
                <div class="card-body" id="searchResults">
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-search fa-2x mb-3"></i>
                        <p>Use the filters above to find your perfect match</p>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
// Update range value displays
document.addEventListener('DOMContentLoaded', function() {
    const ranges = ['minAge', 'maxAge', 'distance', 'minHeight', 'maxHeight'];
    
    ranges.forEach(id => {
        const range = document.getElementById(id);
        const valueSpan = document.getElementById(id + 'Value');
        
        if (range && valueSpan) {
            range.addEventListener('input', function() {
                valueSpan.textContent = this.value;
            });
        }
    });
});

function resetFilters() {
    document.getElementById('advancedSearchForm').reset();
    document.getElementById('minAgeValue').textContent = '25';
    document.getElementById('maxAgeValue').textContent = '35';
    document.getElementById('distanceValue').textContent = '{{ max_radius }}';
    document.getElementById('minHeightValue').textContent = '160';
    document.getElementById('maxHeightValue').textContent = '180';
}

document.getElementById('advancedSearchForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    {% if has_premium_filters %}
    // Collect filter values
    const filters = {
        minAge: document.getElementById('minAge').value,
        maxAge: document.getElementById('maxAge').value,
        distance: document.getElementById('distance').value,
        // Add other filter values...
    };
    
    // Simulate search results
    document.getElementById('searchResults').innerHTML = `
        <div class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Searching...</span>
            </div>
            <p class="mt-2">Searching for matches...</p>
        </div>
    `;
    
    setTimeout(() => {
        document.getElementById('searchResults').innerHTML = `
            <div class="text-center text-success py-4">
                <i class="fas fa-check-circle fa-2x mb-3"></i>
                <p>Found 12 potential matches with your criteria!</p>
                <a href="{{ url_for('discover') }}" class="btn btn-primary">View Matches</a>
            </div>
        `;
    }, 2000);
    {% else %}
    showNotification('Premium subscription required for advanced search!', 'warning');
    {% endif %}
});
</script>
{% endblock %}