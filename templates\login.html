{% extends "base.html" %}

{% block title %}Login - HeartGrid{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <div class="auth-card">
                <div class="text-center mb-4">
                    <i class="fas fa-heart fa-3x text-danger mb-3 heartbeat"></i>
                    <h2 class="fw-bold">Welcome Back</h2>
                    <p class="text-muted">Sign in to continue your journey</p>
                </div>
                
                <form method="POST" class="needs-validation" novalidate>
                    {% csrf_token %}
                    <div class="mb-3">
                        <label for="email" class="form-label">Email Address</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-envelope"></i>
                            </span>
                            <input type="email" class="form-control" id="email" name="email" required>
                            <div class="invalid-feedback">
                                Please enter a valid email address.
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <label for="password" class="form-label">Password</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-lock"></i>
                            </span>
                            <input type="password" class="form-control" id="password" name="password" required>
                            <div class="invalid-feedback">
                                Please enter your password.
                            </div>
                        </div>
                    </div>
                    
                    <button type="submit" class="btn btn-primary w-100 btn-lg mb-3">
                        <i class="fas fa-sign-in-alt me-2"></i>
                        Sign In
                    </button>
                </form>
                
                <div class="divider my-4">
                    <span class="divider-text text-muted">Or continue with</span>
                </div>
                
                <div class="social-auth">
                    <a href="#" class="btn btn-outline-danger w-100 mb-2">
                        <i class="fab fa-google me-2"></i>
                        Continue with Google
                    </a>
                    <a href="#" class="btn btn-outline-primary w-100 mb-3">
                        <i class="fab fa-facebook-f me-2"></i>
                        Continue with Facebook
                    </a>
                </div>
                
                <div class="text-center">
                    <p class="text-muted">
                        Don't have an account?
                        <a href="{% url 'heartgrid_frontend:register_page' %}" class="text-primary fw-bold">Sign up here</a>
                    </p>
                </div>
            </div>
        </div>
        
        <div class="col-md-6 d-none d-md-block">
            <div class="auth-image">
                <img src="https://pixabay.com/get/g78415e52ff17dcc10b86da1065e3cb353ec4592ecedb879b33f46f57466c4da00b470614a80109a37d46b09ca1110b58fb23a1285faa5c533f91d4e96ef604fd_1280.jpg" 
                     alt="Modern Dating" class="img-fluid rounded-lg">
                <div class="auth-overlay">
                    <h4 class="text-white fw-bold mb-2">Find Your Perfect Match</h4>
                    <p class="text-white">Join HeartGrid and discover meaningful connections</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
