"""
WebSocket Consumers for HeartGrid Communications

This module contains WebSocket consumers for real-time messaging,
user presence tracking, and notifications.
"""

import json
import uuid
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from django.contrib.auth import get_user_model
from django.utils import timezone
from .models import Conversation, Message, UserPresence, MessageStatus, ConversationParticipant

User = get_user_model()


class ChatConsumer(AsyncWebsocketConsumer):
    """
    WebSocket consumer for handling real-time chat messages
    """

    async def connect(self):
        """Handle WebSocket connection"""
        self.user = self.scope["user"]
        
        if not self.user.is_authenticated:
            await self.close()
            return

        # Get conversation ID from URL
        self.conversation_id = self.scope['url_route']['kwargs']['conversation_id']
        self.room_group_name = f'chat_{self.conversation_id}'

        # Verify user is participant in conversation
        if not await self.is_participant():
            await self.close()
            return

        # Join room group
        await self.channel_layer.group_add(
            self.room_group_name,
            self.channel_name
        )

        # Set user as online
        await self.set_user_online()

        await self.accept()

        # Send initial conversation data
        await self.send_conversation_data()

    async def disconnect(self, close_code):
        """Handle WebSocket disconnection"""
        if hasattr(self, 'room_group_name'):
            # Leave room group
            await self.channel_layer.group_discard(
                self.room_group_name,
                self.channel_name
            )

        # Update user presence
        if hasattr(self, 'user') and self.user.is_authenticated:
            await self.set_user_offline()

    async def receive(self, text_data):
        """Handle incoming WebSocket messages"""
        try:
            data = json.loads(text_data)
            message_type = data.get('type')

            if message_type == 'chat_message':
                await self.handle_chat_message(data)
            elif message_type == 'typing_start':
                await self.handle_typing_start(data)
            elif message_type == 'typing_stop':
                await self.handle_typing_stop(data)
            elif message_type == 'mark_read':
                await self.handle_mark_read(data)
            elif message_type == 'message_reaction':
                await self.handle_message_reaction(data)

        except json.JSONDecodeError:
            await self.send(text_data=json.dumps({
                'type': 'error',
                'message': 'Invalid JSON format'
            }))

    async def handle_chat_message(self, data):
        """Handle incoming chat messages"""
        content = data.get('message', '').strip()
        message_type = data.get('message_type', 'text')
        
        if not content and message_type == 'text':
            return

        # Create message in database
        message = await self.create_message(content, message_type)
        
        if message:
            # Send message to room group
            await self.channel_layer.group_send(
                self.room_group_name,
                {
                    'type': 'chat_message',
                    'message': await self.serialize_message(message),
                    'sender_id': str(self.user.id)
                }
            )

    async def handle_typing_start(self, data):
        """Handle typing start indicator"""
        await self.set_typing_status(True)
        
        # Broadcast typing status to other participants
        await self.channel_layer.group_send(
            self.room_group_name,
            {
                'type': 'typing_status',
                'user_id': str(self.user.id),
                'user_name': self.user.name,
                'is_typing': True
            }
        )

    async def handle_typing_stop(self, data):
        """Handle typing stop indicator"""
        await self.set_typing_status(False)
        
        # Broadcast typing status to other participants
        await self.channel_layer.group_send(
            self.room_group_name,
            {
                'type': 'typing_status',
                'user_id': str(self.user.id),
                'user_name': self.user.name,
                'is_typing': False
            }
        )

    async def handle_mark_read(self, data):
        """Handle marking messages as read"""
        message_id = data.get('message_id')
        if message_id:
            await self.mark_message_read(message_id)

    async def handle_message_reaction(self, data):
        """Handle message reactions (emoji)"""
        message_id = data.get('message_id')
        emoji = data.get('emoji')
        action = data.get('action', 'add')  # 'add' or 'remove'
        
        if message_id and emoji:
            await self.update_message_reaction(message_id, emoji, action)

    # WebSocket message handlers
    async def chat_message(self, event):
        """Send chat message to WebSocket"""
        await self.send(text_data=json.dumps({
            'type': 'chat_message',
            'message': event['message'],
            'sender_id': event['sender_id']
        }))

    async def typing_status(self, event):
        """Send typing status to WebSocket"""
        # Don't send typing status to the user who is typing
        if event['user_id'] != str(self.user.id):
            await self.send(text_data=json.dumps({
                'type': 'typing_status',
                'user_id': event['user_id'],
                'user_name': event['user_name'],
                'is_typing': event['is_typing']
            }))

    async def user_status(self, event):
        """Send user status update to WebSocket"""
        await self.send(text_data=json.dumps({
            'type': 'user_status',
            'user_id': event['user_id'],
            'status': event['status'],
            'last_seen': event.get('last_seen')
        }))

    async def message_reaction(self, event):
        """Send message reaction update to WebSocket"""
        await self.send(text_data=json.dumps({
            'type': 'message_reaction',
            'message_id': event['message_id'],
            'reactions': event['reactions']
        }))

    # Database operations
    @database_sync_to_async
    def is_participant(self):
        """Check if user is a participant in the conversation"""
        try:
            conversation = Conversation.objects.get(id=self.conversation_id)
            return conversation.participants.filter(id=self.user.id).exists()
        except Conversation.DoesNotExist:
            return False

    @database_sync_to_async
    def create_message(self, content, message_type='text'):
        """Create a new message in the database"""
        try:
            conversation = Conversation.objects.get(id=self.conversation_id)
            message = Message.objects.create(
                conversation=conversation,
                sender=self.user,
                content=content,
                message_type=message_type
            )
            
            # Create message status for all participants except sender
            participants = conversation.participants.exclude(id=self.user.id)
            for participant in participants:
                MessageStatus.objects.create(
                    message=message,
                    user=participant,
                    status='sent'
                )
            
            return message
        except Conversation.DoesNotExist:
            return None

    @database_sync_to_async
    def serialize_message(self, message):
        """Serialize message for WebSocket transmission"""
        return {
            'id': str(message.id),
            'content': message.content,
            'message_type': message.message_type,
            'sender_id': str(message.sender.id),
            'sender_name': message.sender.name,
            'created_at': message.created_at.isoformat(),
            'reactions': message.reactions
        }

    @database_sync_to_async
    def set_user_online(self):
        """Set user status to online"""
        presence, created = UserPresence.objects.get_or_create(user=self.user)
        presence.set_online()

    @database_sync_to_async
    def set_user_offline(self):
        """Set user status to offline"""
        try:
            presence = UserPresence.objects.get(user=self.user)
            presence.set_offline()
        except UserPresence.DoesNotExist:
            pass

    @database_sync_to_async
    def set_typing_status(self, is_typing):
        """Set typing status for user"""
        try:
            presence = UserPresence.objects.get(user=self.user)
            if is_typing:
                # Get the other participant
                conversation = Conversation.objects.get(id=self.conversation_id)
                other_user = conversation.get_other_participant(self.user)
                if other_user:
                    presence.set_typing(other_user)
            else:
                presence.clear_typing()
        except (UserPresence.DoesNotExist, Conversation.DoesNotExist):
            pass

    @database_sync_to_async
    def mark_message_read(self, message_id):
        """Mark a message as read"""
        try:
            message = Message.objects.get(id=message_id)
            status, created = MessageStatus.objects.get_or_create(
                message=message,
                user=self.user,
                defaults={'status': 'read', 'read_at': timezone.now()}
            )
            if not created and status.status != 'read':
                status.mark_as_read()
        except Message.DoesNotExist:
            pass

    @database_sync_to_async
    def update_message_reaction(self, message_id, emoji, action):
        """Add or remove message reaction"""
        try:
            message = Message.objects.get(id=message_id)
            if action == 'add':
                message.add_reaction(self.user, emoji)
            elif action == 'remove':
                message.remove_reaction(self.user)
            
            # Broadcast reaction update
            return message.reactions
        except Message.DoesNotExist:
            return None


class PresenceConsumer(AsyncWebsocketConsumer):
    """
    WebSocket consumer for handling user presence updates
    """

    async def connect(self):
        """Handle WebSocket connection for presence tracking"""
        self.user = self.scope["user"]

        if not self.user.is_authenticated:
            await self.close()
            return

        # Join user's presence group
        self.user_group_name = f'user_{self.user.id}'
        await self.channel_layer.group_add(
            self.user_group_name,
            self.channel_name
        )

        # Set user as online
        await self.set_user_online()

        await self.accept()

        # Broadcast user online status
        await self.broadcast_presence_update('online')

    async def disconnect(self, close_code):
        """Handle WebSocket disconnection"""
        if hasattr(self, 'user_group_name'):
            await self.channel_layer.group_discard(
                self.user_group_name,
                self.channel_name
            )

        # Set user as offline and broadcast
        if hasattr(self, 'user') and self.user.is_authenticated:
            await self.set_user_offline()
            await self.broadcast_presence_update('offline')

    async def receive(self, text_data):
        """Handle incoming presence messages"""
        try:
            data = json.loads(text_data)
            message_type = data.get('type')

            if message_type == 'heartbeat':
                await self.handle_heartbeat()
            elif message_type == 'status_change':
                await self.handle_status_change(data)

        except json.JSONDecodeError:
            pass

    async def handle_heartbeat(self):
        """Handle heartbeat to keep connection alive"""
        await self.update_last_activity()

    async def handle_status_change(self, data):
        """Handle manual status changes (away, etc.)"""
        status = data.get('status', 'online')
        if status in ['online', 'away']:
            await self.update_user_status(status)
            await self.broadcast_presence_update(status)

    async def presence_update(self, event):
        """Send presence update to WebSocket"""
        await self.send(text_data=json.dumps({
            'type': 'presence_update',
            'user_id': event['user_id'],
            'status': event['status'],
            'last_seen': event.get('last_seen')
        }))

    @database_sync_to_async
    def set_user_online(self):
        """Set user status to online"""
        presence, created = UserPresence.objects.get_or_create(user=self.user)
        presence.set_online()

    @database_sync_to_async
    def set_user_offline(self):
        """Set user status to offline"""
        try:
            presence = UserPresence.objects.get(user=self.user)
            presence.set_offline()
        except UserPresence.DoesNotExist:
            pass

    @database_sync_to_async
    def update_last_activity(self):
        """Update user's last activity timestamp"""
        try:
            presence = UserPresence.objects.get(user=self.user)
            presence.last_activity = timezone.now()
            presence.save()
        except UserPresence.DoesNotExist:
            pass

    @database_sync_to_async
    def update_user_status(self, status):
        """Update user's presence status"""
        try:
            presence = UserPresence.objects.get(user=self.user)
            presence.status = status
            presence.last_activity = timezone.now()
            presence.save()
        except UserPresence.DoesNotExist:
            pass

    async def broadcast_presence_update(self, status):
        """Broadcast presence update to relevant users"""
        # Get users who should receive this update (matches, active conversations)
        relevant_users = await self.get_relevant_users()

        for user_id in relevant_users:
            await self.channel_layer.group_send(
                f'user_{user_id}',
                {
                    'type': 'presence_update',
                    'user_id': str(self.user.id),
                    'status': status,
                    'last_seen': timezone.now().isoformat() if status == 'offline' else None
                }
            )

    @database_sync_to_async
    def get_relevant_users(self):
        """Get list of users who should receive presence updates"""
        # Get users from active conversations
        conversation_users = set()

        # Get conversations where this user is a participant
        conversations = Conversation.objects.filter(
            participants=self.user,
            is_active=True
        ).prefetch_related('participants')

        for conversation in conversations:
            for participant in conversation.participants.exclude(id=self.user.id):
                conversation_users.add(str(participant.id))

        return list(conversation_users)


class NotificationConsumer(AsyncWebsocketConsumer):
    """
    WebSocket consumer for handling general notifications
    """

    async def connect(self):
        """Handle WebSocket connection for notifications"""
        self.user = self.scope["user"]

        if not self.user.is_authenticated:
            await self.close()
            return

        # Join user's notification group
        self.user_group_name = f'user_{self.user.id}'
        await self.channel_layer.group_add(
            self.user_group_name,
            self.channel_name
        )

        await self.accept()

    async def disconnect(self, close_code):
        """Handle WebSocket disconnection"""
        if hasattr(self, 'user_group_name'):
            await self.channel_layer.group_discard(
                self.user_group_name,
                self.channel_name
            )

    async def receive(self, text_data):
        """Handle incoming notification messages"""
        try:
            data = json.loads(text_data)
            message_type = data.get('type')

            if message_type == 'mark_notification_read':
                await self.handle_mark_notification_read(data)

        except json.JSONDecodeError:
            pass

    async def handle_mark_notification_read(self, data):
        """Handle marking notifications as read"""
        notification_id = data.get('notification_id')
        # Implement notification read tracking if needed
        pass

    async def send_notification(self, event):
        """Send notification to WebSocket"""
        await self.send(text_data=json.dumps(event['notification']))

    @database_sync_to_async
    def send_conversation_data(self):
        """Send initial conversation data"""
        try:
            conversation = Conversation.objects.get(id=self.conversation_id)
            # Get recent messages
            messages = conversation.messages.filter(is_deleted=False).order_by('-created_at')[:50]
            
            # Get participant info
            participants = []
            for participant in conversation.participants.all():
                presence = getattr(participant, 'presence', None)
                participants.append({
                    'id': str(participant.id),
                    'name': participant.name,
                    'status': presence.status if presence else 'offline',
                    'last_seen': presence.last_seen.isoformat() if presence and presence.last_seen else None
                })
            
            return {
                'conversation_id': str(conversation.id),
                'participants': participants,
                'messages': [self.serialize_message(msg) for msg in reversed(messages)]
            }
        except Conversation.DoesNotExist:
            return None
