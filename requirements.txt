# HeartGrid Dating Platform - Requirements
# Python 3.11+ required

# ===== CORE DEPENDENCIES (Required) =====

# Core Django Framework
Django>=5.0.0,<6.0.0
djangorestframework>=3.14.0,<4.0.0

# Database & Configuration
dj-database-url>=2.1.0
python-decouple>=3.8

# Authentication & Authorization
django-allauth>=0.57.0
django-oauth-toolkit>=1.7.0

# WebSocket & Real-time Communications
channels>=4.0.0
channels-redis>=4.1.0
daphne>=4.0.0

# API & CORS
django-cors-headers>=4.3.0

# Image Processing
Pillow>=11.2.1

# HTTP Requests
requests>=2.32.4

# Static Files & Media
whitenoise>=6.6.0

# Production Server
gunicorn>=23.0.0

# Background Tasks
celery>=5.3.0
redis>=5.0.0

# ===== PRODUCTION DEPENDENCIES (Recommended) =====

# PostgreSQL Database (for production)
psycopg2-binary>=2.9.0

# Security & Performance
django-ratelimit>=4.1.0
asgiref>=3.7.0

# Monitoring & Logging
sentry-sdk>=1.38.0

# ===== DEVELOPMENT DEPENDENCIES (Optional) =====

# Testing Framework
pytest>=7.4.0
pytest-django>=4.5.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0

# Code Quality
flake8>=6.0.0
black>=23.0.0
isort>=5.12.0

# Development Tools
django-extensions>=3.2.0
django-debug-toolbar>=4.2.0

# ===== OPTIONAL ENHANCEMENTS =====

# File Storage (for cloud deployment)
django-storages>=1.14.0
boto3>=1.34.0

# API Documentation
drf-spectacular>=0.27.0

# Caching
django-redis>=5.4.0

# Email Backend
django-ses>=3.5.0

# Form Handling
django-crispy-forms>=2.1.0

# Validation
django-phonenumber-field>=7.3.0
phonenumbers>=8.13.0

# Testing Utilities
factory-boy>=3.3.0
faker>=20.1.0

# WebSocket Testing
websockets>=12.0

# Performance Monitoring
django-silk>=5.0.0

# Health Checks
django-health-check>=3.17.0

# Background Job Monitoring
flower>=2.0.0

# ===== CRYPTOCURRENCY & PAYMENTS =====

# Cryptography for payment processing
cryptography>=41.0.0
pycryptodome>=3.19.0

# ===== UTILITIES =====

# Date/Time handling
python-dateutil>=2.8.0
pytz>=2023.3

# Environment variables
python-dotenv>=1.0.0

# JSON Web Tokens
PyJWT>=2.8.0