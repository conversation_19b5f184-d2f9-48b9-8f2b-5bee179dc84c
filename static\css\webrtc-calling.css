/* WebRTC Calling Styles for HeartGrid */

/* Presence indicators with call availability */
.presence-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid white;
    position: relative;
}

.presence-indicator.online {
    background-color: #10b981; /* green-500 */
}

.presence-indicator.away {
    background-color: #f59e0b; /* amber-500 */
}

.presence-indicator.offline {
    background-color: #6b7280; /* gray-500 */
}

/* Call availability overlay indicators */
.presence-indicator.call-busy::after {
    content: '';
    position: absolute;
    top: -2px;
    right: -2px;
    width: 8px;
    height: 8px;
    background-color: #ef4444; /* red-500 */
    border-radius: 50%;
    border: 1px solid white;
}

.presence-indicator.call-in_call::after {
    content: '';
    position: absolute;
    top: -2px;
    right: -2px;
    width: 8px;
    height: 8px;
    background-color: #3b82f6; /* blue-500 */
    border-radius: 50%;
    border: 1px solid white;
}

/* Call availability text indicators */
.call-availability {
    font-size: 0.75rem;
    padding: 0.125rem 0.375rem;
    border-radius: 0.375rem;
    font-weight: 500;
    text-transform: capitalize;
}

.call-availability.available {
    background-color: #dcfce7; /* green-100 */
    color: #166534; /* green-800 */
}

.call-availability.busy {
    background-color: #fef2f2; /* red-50 */
    color: #991b1b; /* red-800 */
}

.call-availability.in_call {
    background-color: #dbeafe; /* blue-100 */
    color: #1e40af; /* blue-800 */
}

/* Call buttons */
.call-buttons {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.voice-call-btn, .video-call-btn {
    transition: all 0.2s ease;
}

.voice-call-btn:hover:not(:disabled) {
    background-color: #f0fdf4; /* green-50 */
    transform: scale(1.05);
}

.video-call-btn:hover:not(:disabled) {
    background-color: #eff6ff; /* blue-50 */
    transform: scale(1.05);
}

.voice-call-btn:disabled,
.video-call-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Incoming call modal */
.incoming-call-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.incoming-call-content {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    text-align: center;
    max-width: 400px;
    width: 90%;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.caller-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    margin: 0 auto 1rem;
    background-color: #f3f4f6;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: #6b7280;
}

.call-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
}

.answer-btn {
    background-color: #10b981;
    color: white;
    border: none;
    border-radius: 50%;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.answer-btn:hover {
    background-color: #059669;
    transform: scale(1.1);
}

.reject-btn {
    background-color: #ef4444;
    color: white;
    border: none;
    border-radius: 50%;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.reject-btn:hover {
    background-color: #dc2626;
    transform: scale(1.1);
}

/* Active call UI */
.active-call-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #1f2937;
    z-index: 9998;
    display: flex;
    flex-direction: column;
}

.call-header {
    padding: 1rem;
    text-align: center;
    color: white;
    background-color: rgba(0, 0, 0, 0.3);
}

.call-video-container {
    flex: 1;
    position: relative;
    background-color: #000;
}

.remote-video {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.local-video {
    position: absolute;
    top: 1rem;
    right: 1rem;
    width: 150px;
    height: 100px;
    border-radius: 0.5rem;
    border: 2px solid white;
    object-fit: cover;
}

.call-controls {
    padding: 2rem;
    display: flex;
    justify-content: center;
    gap: 1rem;
    background-color: rgba(0, 0, 0, 0.5);
}

.call-control-btn {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    cursor: pointer;
    transition: all 0.2s ease;
    color: white;
}

.mute-btn {
    background-color: #6b7280;
}

.mute-btn.active {
    background-color: #ef4444;
}

.video-btn {
    background-color: #6b7280;
}

.video-btn.active {
    background-color: #ef4444;
}

.speaker-btn {
    background-color: #6b7280;
}

.speaker-btn.active {
    background-color: #3b82f6;
}

.end-call-btn {
    background-color: #ef4444;
}

.call-control-btn:hover {
    transform: scale(1.1);
}

/* Outgoing call UI */
.outgoing-call-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.outgoing-call-content {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    text-align: center;
    max-width: 400px;
    width: 90%;
}

.calling-animation {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f4f6;
    border-radius: 50%;
    border-top-color: #3b82f6;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Responsive design */
@media (max-width: 768px) {
    .local-video {
        width: 100px;
        height: 75px;
        top: 0.5rem;
        right: 0.5rem;
    }
    
    .call-controls {
        padding: 1rem;
        gap: 0.5rem;
    }
    
    .call-control-btn {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }
    
    .incoming-call-content,
    .outgoing-call-content {
        padding: 1.5rem;
        margin: 1rem;
    }
}

/* Notification styles */
.call-notification {
    background-color: #3b82f6;
    color: white;
    border-radius: 0.5rem;
    padding: 1rem;
    margin: 0.5rem;
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
}

.call-notification.missed {
    background-color: #ef4444;
}

.call-notification.answered {
    background-color: #10b981;
}

/* Audio-only call styles */
.audio-call-container {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: white;
    text-align: center;
}

.audio-call-avatar {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    margin-bottom: 2rem;
}

.call-duration {
    font-size: 1.5rem;
    font-weight: 300;
    margin-bottom: 1rem;
}

.call-status {
    font-size: 1rem;
    opacity: 0.8;
    margin-bottom: 2rem;
}
