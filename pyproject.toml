[project]
name = "heartgrid-django"
version = "0.1.0"
description = "HeartGrid Dating Platform - Django Version"
requires-python = ">=3.11"
dependencies = [
    "django>=5.0.0",
    "djangorestframework>=3.14.0",
    "django-cors-headers>=4.3.0",
    "django-oauth-toolkit>=1.7.0",
    "django-allauth>=0.57.0",
    "pillow>=11.2.1",
    "requests>=2.32.4",
    "python-decouple>=3.8",
    "dj-database-url>=2.1.0",
    "whitenoise>=6.6.0",
    "gunicorn>=23.0.0",
    "celery>=5.3.0",
    "redis>=5.0.0",
    "channels>=4.0.0",
    "channels-redis>=4.1.0",
    "daphne>=4.0.0",
]
