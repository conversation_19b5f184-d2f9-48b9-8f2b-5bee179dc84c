/**
 * WebRTC Manager for HeartGrid Communications
 * 
 * Handles peer-to-peer voice and video calling functionality
 * using WebRTC technology integrated with Django Channels WebSocket infrastructure.
 */

class WebRTCManager {
    constructor(communicationsClient) {
        this.communicationsClient = communicationsClient;
        this.peerConnection = null;
        this.localStream = null;
        this.remoteStream = null;
        this.currentCall = null;
        this.isInitiator = false;
        
        // ICE servers configuration with production-ready setup
        this.iceServers = this.getICEServerConfiguration();
        
        this.setupEventHandlers();
        this.createCallUI();
    }

    getICEServerConfiguration() {
        // Production ICE server configuration
        const iceServers = [
            // Google STUN servers (free)
            { urls: 'stun:stun.l.google.com:19302' },
            { urls: 'stun:stun1.l.google.com:19302' },
            { urls: 'stun:stun2.l.google.com:19302' },
            { urls: 'stun:stun3.l.google.com:19302' },
            { urls: 'stun:stun4.l.google.com:19302' },

            // Additional public STUN servers for redundancy
            { urls: 'stun:stun.stunprotocol.org:3478' },
            { urls: 'stun:stun.voiparound.com' },
            { urls: 'stun:stun.voipbuster.com' },
            { urls: 'stun:stun.voipstunt.com' },
            { urls: 'stun:stun.voxgratia.org' }
        ];

        // Add TURN servers if configured (for production environments behind NAT/firewalls)
        const turnConfig = this.getTURNServerConfiguration();
        if (turnConfig.length > 0) {
            iceServers.push(...turnConfig);
        }

        return {
            iceServers: iceServers,
            iceCandidatePoolSize: 10,
            bundlePolicy: 'max-bundle',
            rtcpMuxPolicy: 'require'
        };
    }

    getTURNServerConfiguration() {
        // TURN server configuration for production
        // These should be configured via environment variables or Django settings
        const turnServers = [];

        // Check if TURN servers are configured in the global scope
        if (typeof window.HEARTGRID_TURN_CONFIG !== 'undefined') {
            const turnConfig = window.HEARTGRID_TURN_CONFIG;

            if (turnConfig.servers && turnConfig.servers.length > 0) {
                turnConfig.servers.forEach(server => {
                    turnServers.push({
                        urls: server.urls,
                        username: server.username,
                        credential: server.credential,
                        credentialType: server.credentialType || 'password'
                    });
                });
            }
        }

        // Fallback to environment-based configuration
        if (turnServers.length === 0) {
            // Example TURN server configuration (replace with your actual TURN servers)
            const turnUrl = this.getEnvironmentVariable('TURN_SERVER_URL');
            const turnUsername = this.getEnvironmentVariable('TURN_USERNAME');
            const turnCredential = this.getEnvironmentVariable('TURN_CREDENTIAL');

            if (turnUrl && turnUsername && turnCredential) {
                turnServers.push({
                    urls: turnUrl,
                    username: turnUsername,
                    credential: turnCredential
                });
            }
        }

        return turnServers;
    }

    getEnvironmentVariable(name) {
        // Get environment variable from meta tags or global scope
        const metaTag = document.querySelector(`meta[name="${name.toLowerCase()}"]`);
        if (metaTag) {
            return metaTag.getAttribute('content');
        }

        // Check global window object
        return window[name] || null;
    }
    
    setupEventHandlers() {
        // Listen for WebRTC-related WebSocket messages
        this.communicationsClient.addMessageHandler('incoming_call', (data) => {
            this.handleIncomingCall(data);
        });
        
        this.communicationsClient.addMessageHandler('call_answered', (data) => {
            this.handleCallAnswered(data);
        });
        
        this.communicationsClient.addMessageHandler('call_rejected', (data) => {
            this.handleCallRejected(data);
        });
        
        this.communicationsClient.addMessageHandler('call_ended', (data) => {
            this.handleCallEnded(data);
        });
        
        this.communicationsClient.addMessageHandler('webrtc_offer', (data) => {
            this.handleWebRTCOffer(data);
        });
        
        this.communicationsClient.addMessageHandler('webrtc_answer', (data) => {
            this.handleWebRTCAnswer(data);
        });
        
        this.communicationsClient.addMessageHandler('webrtc_ice_candidate', (data) => {
            this.handleICECandidate(data);
        });
    }
    
    async initiateCall(userId, callType = 'voice') {
        try {
            console.log(`Initiating ${callType} call to user ${userId}`);
            
            // Check if already in a call
            if (this.currentCall) {
                throw new Error('Already in a call');
            }
            
            // Get user media
            this.localStream = await this.getUserMedia(callType);
            this.isInitiator = true;
            
            // Create peer connection
            this.createPeerConnection();
            
            // Add local stream to peer connection
            this.localStream.getTracks().forEach(track => {
                this.peerConnection.addTrack(track, this.localStream);
            });
            
            // Store call information
            this.currentCall = {
                userId: userId,
                type: callType,
                state: 'initiating'
            };
            
            // Send call initiation through WebSocket
            this.communicationsClient.sendMessage({
                type: 'call_initiate',
                callee_id: userId,
                call_type: callType
            });
            
            // Show outgoing call UI
            this.showOutgoingCallUI(userId, callType);
            
        } catch (error) {
            console.error('Error initiating call:', error);
            this.handleCallError(error);
            this.cleanup();
        }
    }
    
    async answerCall(callData) {
        try {
            console.log('Answering call:', callData);
            
            // Get user media
            this.localStream = await this.getUserMedia(callData.call_type);
            this.isInitiator = false;
            
            // Create peer connection
            this.createPeerConnection();
            
            // Add local stream
            this.localStream.getTracks().forEach(track => {
                this.peerConnection.addTrack(track, this.localStream);
            });
            
            // Store call information
            this.currentCall = {
                sessionId: callData.call_session_id,
                userId: callData.caller_id,
                type: callData.call_type,
                state: 'answering'
            };
            
            // Send answer through WebSocket
            this.communicationsClient.sendMessage({
                type: 'call_answer',
                call_session_id: callData.call_session_id
            });
            
            // Hide incoming call modal and show active call UI
            this.hideIncomingCallModal();
            this.showActiveCallUI();
            
        } catch (error) {
            console.error('Error answering call:', error);
            this.rejectCall(callData.call_session_id);
        }
    }
    
    rejectCall(callSessionId) {
        console.log('Rejecting call:', callSessionId);
        
        this.communicationsClient.sendMessage({
            type: 'call_reject',
            call_session_id: callSessionId
        });
        
        this.hideIncomingCallModal();
        this.cleanup();
    }
    
    endCall(reason = 'normal') {
        console.log('Ending call:', reason);
        
        if (this.currentCall && this.currentCall.sessionId) {
            this.communicationsClient.sendMessage({
                type: 'call_end',
                call_session_id: this.currentCall.sessionId,
                reason: reason
            });
        }
        
        this.hideActiveCallUI();
        this.cleanup();
    }
    
    createPeerConnection() {
        this.peerConnection = new RTCPeerConnection(this.iceServers);
        
        // Handle ICE candidates
        this.peerConnection.onicecandidate = (event) => {
            if (event.candidate && this.currentCall) {
                console.log('Sending ICE candidate');
                this.communicationsClient.sendMessage({
                    type: 'webrtc_ice_candidate',
                    candidate: event.candidate,
                    call_session_id: this.currentCall.sessionId
                });
            }
        };
        
        // Handle remote stream
        this.peerConnection.ontrack = (event) => {
            console.log('Received remote stream');
            this.remoteStream = event.streams[0];
            this.displayRemoteStream();
        };
        
        // Handle connection state changes
        this.peerConnection.onconnectionstatechange = () => {
            console.log('Connection state:', this.peerConnection.connectionState);
            
            switch (this.peerConnection.connectionState) {
                case 'connected':
                    this.onCallConnected();
                    break;
                case 'disconnected':
                case 'failed':
                    this.endCall('connection_failed');
                    break;
                case 'closed':
                    this.cleanup();
                    break;
            }
        };
        
        // Handle ICE connection state changes
        this.peerConnection.oniceconnectionstatechange = () => {
            console.log('ICE connection state:', this.peerConnection.iceConnectionState);
            
            if (this.peerConnection.iceConnectionState === 'failed') {
                this.endCall('ice_failed');
            }
        };
    }
    
    async getUserMedia(callType) {
        const constraints = {
            audio: true,
            video: callType === 'video'
        };
        
        try {
            const stream = await navigator.mediaDevices.getUserMedia(constraints);
            this.displayLocalStream(stream);
            return stream;
        } catch (error) {
            console.error('Error accessing media devices:', error);
            throw new Error('Could not access camera/microphone');
        }
    }
    
    displayLocalStream(stream) {
        const localVideo = document.getElementById('local-video');
        if (localVideo) {
            localVideo.srcObject = stream;
            localVideo.muted = true; // Prevent echo
        }
    }
    
    displayRemoteStream() {
        const remoteVideo = document.getElementById('remote-video');
        if (remoteVideo && this.remoteStream) {
            remoteVideo.srcObject = this.remoteStream;
        }
    }
    
    // WebSocket message handlers
    
    handleIncomingCall(data) {
        console.log('Incoming call:', data);
        
        if (this.currentCall) {
            // Already in a call, reject automatically
            this.rejectCall(data.call_session_id);
            return;
        }
        
        this.showIncomingCallModal(data);
    }
    
    async handleCallAnswered(data) {
        console.log('Call answered:', data);
        
        if (this.currentCall) {
            this.currentCall.sessionId = data.call_session_id;
            this.currentCall.state = 'connecting';
            
            // Create and send WebRTC offer
            try {
                const offer = await this.peerConnection.createOffer();
                await this.peerConnection.setLocalDescription(offer);
                
                this.communicationsClient.sendMessage({
                    type: 'webrtc_offer',
                    offer: offer,
                    call_session_id: data.call_session_id
                });
                
                this.showActiveCallUI();
                
            } catch (error) {
                console.error('Error creating offer:', error);
                this.endCall('offer_failed');
            }
        }
    }
    
    handleCallRejected(data) {
        console.log('Call rejected:', data);
        this.showCallRejectedMessage();
        this.cleanup();
    }
    
    handleCallEnded(data) {
        console.log('Call ended:', data);
        this.showCallEndedMessage(data.reason);
        this.hideActiveCallUI();
        this.cleanup();
    }
    
    async handleWebRTCOffer(data) {
        console.log('Received WebRTC offer');
        
        try {
            await this.peerConnection.setRemoteDescription(data.offer);
            
            // Create and send answer
            const answer = await this.peerConnection.createAnswer();
            await this.peerConnection.setLocalDescription(answer);
            
            this.communicationsClient.sendMessage({
                type: 'webrtc_answer',
                answer: answer,
                call_session_id: data.call_session_id
            });
            
        } catch (error) {
            console.error('Error handling WebRTC offer:', error);
            this.endCall('offer_handling_failed');
        }
    }
    
    async handleWebRTCAnswer(data) {
        console.log('Received WebRTC answer');
        
        try {
            await this.peerConnection.setRemoteDescription(data.answer);
        } catch (error) {
            console.error('Error handling WebRTC answer:', error);
            this.endCall('answer_handling_failed');
        }
    }
    
    async handleICECandidate(data) {
        console.log('Received ICE candidate');
        
        try {
            await this.peerConnection.addIceCandidate(data.candidate);
        } catch (error) {
            console.error('Error adding ICE candidate:', error);
        }
    }
    
    // UI Management Methods
    
    onCallConnected() {
        console.log('Call connected successfully');
        if (this.currentCall) {
            this.currentCall.state = 'connected';
        }
        this.updateCallStatus('Connected');
    }
    
    cleanup() {
        console.log('Cleaning up WebRTC resources');
        
        // Stop local stream
        if (this.localStream) {
            this.localStream.getTracks().forEach(track => track.stop());
            this.localStream = null;
        }
        
        // Close peer connection
        if (this.peerConnection) {
            this.peerConnection.close();
            this.peerConnection = null;
        }
        
        // Clear remote stream
        this.remoteStream = null;
        
        // Reset call state
        this.currentCall = null;
        this.isInitiator = false;
        
        // Clear video elements
        const localVideo = document.getElementById('local-video');
        const remoteVideo = document.getElementById('remote-video');
        
        if (localVideo) localVideo.srcObject = null;
        if (remoteVideo) remoteVideo.srcObject = null;
    }
    
    handleCallError(error) {
        console.error('Call error:', error);

        // Show error message to user
        this.showErrorMessage(error.message || 'An error occurred during the call');

        // Clean up resources
        this.cleanup();
    }

    // UI Creation and Management Methods

    createCallUI() {
        // Create call UI elements if they don't exist
        if (!document.getElementById('call-ui-container')) {
            const callUIHTML = `
                <!-- Incoming Call Modal -->
                <div id="incoming-call-modal" class="modal" style="display: none;">
                    <div class="modal-box">
                        <h3 class="font-bold text-lg">Incoming Call</h3>
                        <div class="py-4">
                            <div class="flex items-center space-x-4">
                                <div class="avatar">
                                    <div class="w-16 rounded-full">
                                        <img id="caller-avatar" src="" alt="Caller">
                                    </div>
                                </div>
                                <div>
                                    <p class="font-semibold" id="caller-name"></p>
                                    <p class="text-sm opacity-70" id="call-type-label"></p>
                                </div>
                            </div>
                        </div>
                        <div class="modal-action">
                            <button id="answer-call-btn" class="btn btn-success">
                                <i class="fas fa-phone"></i> Answer
                            </button>
                            <button id="reject-call-btn" class="btn btn-error">
                                <i class="fas fa-phone-slash"></i> Decline
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Active Call UI -->
                <div id="active-call-ui" class="fixed inset-0 bg-black z-50" style="display: none;">
                    <div class="h-full flex flex-col">
                        <!-- Remote Video -->
                        <div class="flex-1 relative">
                            <video id="remote-video" class="w-full h-full object-cover" autoplay playsinline></video>

                            <!-- Local Video (Picture-in-Picture) -->
                            <div class="absolute top-4 right-4 w-32 h-24 bg-gray-800 rounded-lg overflow-hidden">
                                <video id="local-video" class="w-full h-full object-cover" autoplay playsinline muted></video>
                            </div>

                            <!-- Call Status -->
                            <div class="absolute top-4 left-4 bg-black bg-opacity-50 text-white px-3 py-1 rounded-lg">
                                <span id="call-status">Connecting...</span>
                            </div>
                        </div>

                        <!-- Call Controls -->
                        <div class="bg-gray-900 p-6">
                            <div class="flex justify-center space-x-6">
                                <button id="mute-btn" class="btn btn-circle btn-lg bg-gray-700 hover:bg-gray-600">
                                    <i class="fas fa-microphone text-white"></i>
                                </button>
                                <button id="video-toggle-btn" class="btn btn-circle btn-lg bg-gray-700 hover:bg-gray-600">
                                    <i class="fas fa-video text-white"></i>
                                </button>
                                <button id="end-call-btn" class="btn btn-circle btn-lg btn-error">
                                    <i class="fas fa-phone-slash text-white"></i>
                                </button>
                                <button id="speaker-btn" class="btn btn-circle btn-lg bg-gray-700 hover:bg-gray-600">
                                    <i class="fas fa-volume-up text-white"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Outgoing Call UI -->
                <div id="outgoing-call-ui" class="modal" style="display: none;">
                    <div class="modal-box">
                        <h3 class="font-bold text-lg">Calling...</h3>
                        <div class="py-4">
                            <div class="flex items-center space-x-4">
                                <div class="avatar">
                                    <div class="w-16 rounded-full">
                                        <img id="callee-avatar" src="" alt="Callee">
                                    </div>
                                </div>
                                <div>
                                    <p class="font-semibold" id="callee-name"></p>
                                    <p class="text-sm opacity-70" id="outgoing-call-type-label"></p>
                                </div>
                            </div>
                        </div>
                        <div class="modal-action">
                            <button id="cancel-call-btn" class="btn btn-error">
                                <i class="fas fa-phone-slash"></i> Cancel
                            </button>
                        </div>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', callUIHTML);
            this.setupCallUIEventHandlers();
        }
    }

    setupCallUIEventHandlers() {
        // Answer call button
        document.getElementById('answer-call-btn').addEventListener('click', () => {
            if (this.pendingCall) {
                this.answerCall(this.pendingCall);
            }
        });

        // Reject call button
        document.getElementById('reject-call-btn').addEventListener('click', () => {
            if (this.pendingCall) {
                this.rejectCall(this.pendingCall.call_session_id);
            }
        });

        // Cancel outgoing call button
        document.getElementById('cancel-call-btn').addEventListener('click', () => {
            this.endCall('cancelled');
        });

        // End call button
        document.getElementById('end-call-btn').addEventListener('click', () => {
            this.endCall();
        });

        // Mute button
        document.getElementById('mute-btn').addEventListener('click', () => {
            this.toggleMute();
        });

        // Video toggle button
        document.getElementById('video-toggle-btn').addEventListener('click', () => {
            this.toggleVideo();
        });

        // Speaker button
        document.getElementById('speaker-btn').addEventListener('click', () => {
            this.toggleSpeaker();
        });
    }

    showIncomingCallModal(callData) {
        this.pendingCall = callData;

        // Update modal content
        document.getElementById('caller-name').textContent = callData.caller_name;
        document.getElementById('call-type-label').textContent =
            callData.call_type === 'video' ? 'Video Call' : 'Voice Call';

        // Show modal
        document.getElementById('incoming-call-modal').style.display = 'flex';

        // Play ringtone
        this.playRingtone();
    }

    hideIncomingCallModal() {
        document.getElementById('incoming-call-modal').style.display = 'none';
        this.stopRingtone();
        this.pendingCall = null;
    }

    showOutgoingCallUI(userId, callType) {
        // Update UI content
        document.getElementById('callee-name').textContent = `User ${userId}`;
        document.getElementById('outgoing-call-type-label').textContent =
            callType === 'video' ? 'Video Call' : 'Voice Call';

        // Show modal
        document.getElementById('outgoing-call-ui').style.display = 'flex';

        // Play calling tone
        this.playCallingTone();
    }

    hideOutgoingCallUI() {
        document.getElementById('outgoing-call-ui').style.display = 'none';
        this.stopCallingTone();
    }

    showActiveCallUI() {
        this.hideIncomingCallModal();
        this.hideOutgoingCallUI();
        document.getElementById('active-call-ui').style.display = 'block';

        // Hide video controls if voice call
        if (this.currentCall && this.currentCall.type === 'voice') {
            document.getElementById('video-toggle-btn').style.display = 'none';
            document.getElementById('local-video').style.display = 'none';
            document.getElementById('remote-video').style.display = 'none';
        }
    }

    hideActiveCallUI() {
        document.getElementById('active-call-ui').style.display = 'none';
    }

    updateCallStatus(status) {
        const statusElement = document.getElementById('call-status');
        if (statusElement) {
            statusElement.textContent = status;
        }
    }

    // Call Control Methods

    toggleMute() {
        if (this.localStream) {
            const audioTrack = this.localStream.getAudioTracks()[0];
            if (audioTrack) {
                audioTrack.enabled = !audioTrack.enabled;
                const muteBtn = document.getElementById('mute-btn');
                const icon = muteBtn.querySelector('i');

                if (audioTrack.enabled) {
                    icon.className = 'fas fa-microphone text-white';
                    muteBtn.classList.remove('btn-error');
                    muteBtn.classList.add('bg-gray-700');
                } else {
                    icon.className = 'fas fa-microphone-slash text-white';
                    muteBtn.classList.add('btn-error');
                    muteBtn.classList.remove('bg-gray-700');
                }
            }
        }
    }

    toggleVideo() {
        if (this.localStream && this.currentCall && this.currentCall.type === 'video') {
            const videoTrack = this.localStream.getVideoTracks()[0];
            if (videoTrack) {
                videoTrack.enabled = !videoTrack.enabled;
                const videoBtn = document.getElementById('video-toggle-btn');
                const icon = videoBtn.querySelector('i');

                if (videoTrack.enabled) {
                    icon.className = 'fas fa-video text-white';
                    videoBtn.classList.remove('btn-error');
                    videoBtn.classList.add('bg-gray-700');
                } else {
                    icon.className = 'fas fa-video-slash text-white';
                    videoBtn.classList.add('btn-error');
                    videoBtn.classList.remove('bg-gray-700');
                }
            }
        }
    }

    toggleSpeaker() {
        // Speaker toggle implementation would depend on browser support
        // This is a placeholder for speaker functionality
        const speakerBtn = document.getElementById('speaker-btn');
        const icon = speakerBtn.querySelector('i');

        // Toggle visual state
        if (icon.className.includes('fa-volume-up')) {
            icon.className = 'fas fa-volume-down text-white';
        } else {
            icon.className = 'fas fa-volume-up text-white';
        }
    }

    // Audio feedback methods

    playRingtone() {
        // Implementation for playing ringtone
        console.log('Playing ringtone...');
    }

    stopRingtone() {
        // Implementation for stopping ringtone
        console.log('Stopping ringtone...');
    }

    playCallingTone() {
        // Implementation for playing calling tone
        console.log('Playing calling tone...');
    }

    stopCallingTone() {
        // Implementation for stopping calling tone
        console.log('Stopping calling tone...');
    }

    showCallRejectedMessage() {
        this.showErrorMessage('Call was declined');
    }

    showCallEndedMessage(reason) {
        let message = 'Call ended';
        if (reason === 'connection_failed') {
            message = 'Call ended due to connection issues';
        } else if (reason === 'ice_failed') {
            message = 'Call ended due to network issues';
        }
        this.showErrorMessage(message);
    }

    showErrorMessage(message) {
        // Create and show error toast/notification
        console.error('Call error:', message);

        // You can integrate with your existing notification system here
        if (window.showNotification) {
            window.showNotification(message, 'error');
        } else {
            alert(message); // Fallback
        }
    }
}

// Export for use in other modules
window.WebRTCManager = WebRTCManager;
