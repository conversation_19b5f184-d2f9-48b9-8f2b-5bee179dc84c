#!/usr/bin/env python
"""
Comprehensive test script for HeartGrid Communications features
Tests messaging, WebRTC calling, notifications, and user presence
"""

import os
import sys
import django
import json
from datetime import datetime, timedelta

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'heartgrid_django.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.test import TestCase
from django.db import transaction
from communications.models import (
    Conversation, Message, MessageStatus, UserPresence, 
    CallSession, UserCallSettings
)
from communications.webrtc_config import WebRTCConfig
from communications.notifications import NotificationManager

User = get_user_model()


class CommunicationsTestSuite:
    """Comprehensive test suite for all communications features"""
    
    def __init__(self):
        self.results = {
            'passed': 0,
            'failed': 0,
            'errors': [],
            'tests': []
        }
        self.test_users = []
        self.test_conversations = []
    
    def log_test(self, test_name, passed, error=None, details=None):
        """Log test result"""
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{status}: {test_name}")
        
        test_result = {
            'name': test_name,
            'passed': passed,
            'error': error,
            'details': details
        }
        self.results['tests'].append(test_result)
        
        if passed:
            self.results['passed'] += 1
        else:
            self.results['failed'] += 1
            if error:
                print(f"   Error: {error}")
                self.results['errors'].append(f"{test_name}: {error}")
        
        if details:
            print(f"   Details: {details}")
    
    def setup_test_data(self):
        """Create test users and conversations"""
        print("\n🔧 Setting up test data...")
        
        try:
            # Create test users
            self.user1 = User.objects.create_user(
                email='<EMAIL>',
                name='Alice Smith',
                password='testpass123'
            )
            self.user2 = User.objects.create_user(
                email='<EMAIL>',
                name='Bob Jones',
                password='testpass123'
            )
            self.user3 = User.objects.create_user(
                email='<EMAIL>',
                name='Carol Wilson',
                password='testpass123'
            )
            
            self.test_users = [self.user1, self.user2, self.user3]
            
            # Create test conversation
            self.conversation = Conversation.objects.create(
                conversation_type='direct'
            )
            self.conversation.participants.add(self.user1, self.user2)
            self.test_conversations = [self.conversation]
            
            self.log_test("Test data setup", True, details=f"Created {len(self.test_users)} users and {len(self.test_conversations)} conversations")
            
        except Exception as e:
            self.log_test("Test data setup", False, str(e))
            return False
        
        return True
    
    def test_user_models(self):
        """Test user model functionality"""
        print("\n👤 Testing User Models...")
        
        try:
            # Test user creation
            user_count = User.objects.filter(username__startswith='test_user_').count()
            self.log_test("User model creation", user_count == 3, details=f"Created {user_count} test users")
            
            # Test user attributes
            user = self.user1
            has_required_fields = all([
                hasattr(user, 'email'),
                hasattr(user, 'name'),
                hasattr(user, 'id'),
                hasattr(user, 'created_at')
            ])
            self.log_test("User model attributes", has_required_fields)
            
        except Exception as e:
            self.log_test("User model functionality", False, str(e))
    
    def test_conversation_models(self):
        """Test conversation model functionality"""
        print("\n💬 Testing Conversation Models...")
        
        try:
            # Test conversation creation
            conv = self.conversation
            self.log_test("Conversation creation", conv is not None)
            
            # Test participants
            participant_count = conv.participants.count()
            self.log_test("Conversation participants", participant_count == 2, details=f"{participant_count} participants")
            
            # Test conversation types
            valid_type = conv.conversation_type in ['direct', 'match']
            self.log_test("Conversation type validation", valid_type, details=f"Type: {conv.conversation_type}")
            
        except Exception as e:
            self.log_test("Conversation model functionality", False, str(e))
    
    def test_message_models(self):
        """Test message model functionality"""
        print("\n📝 Testing Message Models...")
        
        try:
            # Create test message
            message = Message.objects.create(
                conversation=self.conversation,
                sender=self.user1,
                content="Hello, this is a test message!",
                message_type='text'
            )
            
            self.log_test("Message creation", message is not None)
            
            # Test message attributes
            has_required_fields = all([
                message.conversation == self.conversation,
                message.sender == self.user1,
                message.content == "Hello, this is a test message!",
                message.message_type == 'text',
                message.created_at is not None
            ])
            self.log_test("Message attributes", has_required_fields)
            
            # Test message status
            status = MessageStatus.objects.create(
                message=message,
                user=self.user2,
                status='delivered'
            )
            
            self.log_test("Message status creation", status is not None)
            
            # Test message status update
            status.status = 'read'
            status.read_at = datetime.now()
            status.save()
            
            status.refresh_from_db()
            self.log_test("Message status update", status.status == 'read' and status.read_at is not None)
            
        except Exception as e:
            self.log_test("Message model functionality", False, str(e))
    
    def test_user_presence_models(self):
        """Test user presence model functionality"""
        print("\n🟢 Testing User Presence Models...")
        
        try:
            # Create user presence
            presence = UserPresence.objects.create(
                user=self.user1,
                status='online',
                call_availability='available'
            )
            
            self.log_test("User presence creation", presence is not None)
            
            # Test presence status updates
            presence.status = 'away'
            presence.call_availability = 'busy'
            presence.save()
            
            presence.refresh_from_db()
            status_updated = presence.status == 'away' and presence.call_availability == 'busy'
            self.log_test("Presence status updates", status_updated)
            
            # Test last seen tracking
            presence.last_seen = datetime.now()
            presence.save()
            
            self.log_test("Last seen tracking", presence.last_seen is not None)
            
        except Exception as e:
            self.log_test("User presence functionality", False, str(e))
    
    def test_call_session_models(self):
        """Test call session model functionality"""
        print("\n📞 Testing Call Session Models...")
        
        try:
            # Create call session
            call_session = CallSession.objects.create(
                caller=self.user1,
                callee=self.user2,
                conversation=self.conversation,
                call_type='voice',
                call_state='initiating'
            )
            
            self.log_test("Call session creation", call_session is not None)
            
            # Test call state transitions
            call_session.call_state = 'ringing'
            call_session.save()
            
            call_session.call_state = 'active'
            call_session.save()
            
            call_session.call_state = 'ended'
            call_session.save()
            
            call_session.refresh_from_db()
            self.log_test("Call state transitions", call_session.call_state == 'ended')
            
            # Test WebRTC data storage
            offer_data = {
                'type': 'offer',
                'sdp': 'v=0\r\no=- 123456789 2 IN IP4 127.0.0.1\r\n...'
            }
            call_session.webrtc_offer = offer_data
            call_session.save()
            
            call_session.refresh_from_db()
            webrtc_stored = call_session.webrtc_offer['type'] == 'offer'
            self.log_test("WebRTC data storage", webrtc_stored)
            
            # Test ICE candidates storage
            ice_candidate = {
                'candidate': 'candidate:1 1 UDP 2130706431 ************* 54400 typ host',
                'sdpMLineIndex': 0,
                'sdpMid': 'audio'
            }
            call_session.caller_ice_candidates.append(ice_candidate)
            call_session.save()
            
            call_session.refresh_from_db()
            ice_stored = len(call_session.caller_ice_candidates) == 1
            self.log_test("ICE candidates storage", ice_stored)
            
        except Exception as e:
            self.log_test("Call session functionality", False, str(e))
    
    def test_user_call_settings(self):
        """Test user call settings functionality"""
        print("\n⚙️ Testing User Call Settings...")
        
        try:
            # Create call settings
            settings = UserCallSettings.objects.create(
                user=self.user1,
                voice_calls_enabled=True,
                video_calls_enabled=True,
                auto_answer_enabled=False
            )
            
            self.log_test("Call settings creation", settings is not None)
            
            # Test permission methods
            can_voice = settings.can_receive_voice_calls()
            can_video = settings.can_receive_video_calls()
            
            self.log_test("Voice call permissions", can_voice)
            self.log_test("Video call permissions", can_video)
            
            # Test settings update
            settings.voice_calls_enabled = False
            settings.save()
            
            settings.refresh_from_db()
            voice_disabled = not settings.can_receive_voice_calls()
            self.log_test("Call settings update", voice_disabled)
            
        except Exception as e:
            self.log_test("User call settings functionality", False, str(e))
    
    def test_webrtc_configuration(self):
        """Test WebRTC configuration"""
        print("\n🌐 Testing WebRTC Configuration...")
        
        try:
            # Test configuration retrieval
            config = WebRTCConfig.get_webrtc_configuration()
            self.log_test("WebRTC config retrieval", config is not None)
            
            # Test ICE servers
            ice_servers = config.get('iceServers', [])
            has_stun = any(server['urls'].startswith('stun:') for server in ice_servers)
            self.log_test("STUN servers configured", has_stun, details=f"{len(ice_servers)} ICE servers")
            
            # Test configuration validation
            is_valid, errors = WebRTCConfig.validate_configuration()
            self.log_test("Configuration validation", is_valid, details='; '.join(errors) if errors else "No errors")
            
            # Test media constraints
            constraints = WebRTCConfig.get_media_constraints()
            has_audio = 'audio' in constraints
            has_video = 'video' in constraints
            self.log_test("Media constraints", has_audio and has_video)
            
        except Exception as e:
            self.log_test("WebRTC configuration", False, str(e))
    
    def test_notification_system(self):
        """Test notification system"""
        print("\n🔔 Testing Notification System...")
        
        try:
            # Test notification manager
            notification_manager = NotificationManager()
            self.log_test("NotificationManager instantiation", notification_manager is not None)
            
            # Test call availability notification
            notification_manager.send_call_availability_notification(self.user1, 'busy')
            self.log_test("Call availability notification", True)
            
            # Test call notification data structure
            call_session = CallSession.objects.create(
                caller=self.user1,
                callee=self.user2,
                conversation=self.conversation,
                call_type='voice'
            )
            
            notification_data = {
                'type': 'incoming_call',
                'call_session_id': str(call_session.id),
                'caller_id': str(self.user1.id),
                'caller_name': self.user1.name,
                'call_type': 'voice'
            }
            
            # Should not raise exceptions
            notification_manager.send_call_notification(self.user2, 'incoming', notification_data)
            self.log_test("Call notification creation", True)
            
        except Exception as e:
            self.log_test("Notification system", False, str(e))
    
    def cleanup_test_data(self):
        """Clean up test data"""
        print("\n🧹 Cleaning up test data...")
        
        try:
            # Delete test data
            User.objects.filter(email__startswith='user').filter(email__endswith='@test.com').delete()
            # Conversations will be deleted via cascade when users are deleted
            
            self.log_test("Test data cleanup", True)
            
        except Exception as e:
            self.log_test("Test data cleanup", False, str(e))
    
    def run_all_tests(self):
        """Run all communication tests"""
        print("🚀 Starting Comprehensive Communications Testing")
        print("=" * 60)
        
        # Setup
        if not self.setup_test_data():
            print("❌ Failed to setup test data. Aborting tests.")
            return False
        
        # Run all tests
        self.test_user_models()
        self.test_conversation_models()
        self.test_message_models()
        self.test_user_presence_models()
        self.test_call_session_models()
        self.test_user_call_settings()
        self.test_webrtc_configuration()
        self.test_notification_system()
        
        # Cleanup
        self.cleanup_test_data()
        
        # Print summary
        print("\n" + "=" * 60)
        print("🏁 Test Summary")
        print("=" * 60)
        print(f"✅ Passed: {self.results['passed']}")
        print(f"❌ Failed: {self.results['failed']}")
        
        if self.results['errors']:
            print("\n🔍 Error Details:")
            for error in self.results['errors']:
                print(f"   • {error}")
        
        success_rate = (self.results['passed'] / (self.results['passed'] + self.results['failed'])) * 100 if (self.results['passed'] + self.results['failed']) > 0 else 0
        print(f"\n📊 Success Rate: {success_rate:.1f}%")
        
        if success_rate >= 95:
            print("🎉 Excellent! All communication features are working perfectly.")
        elif success_rate >= 85:
            print("👍 Good! Minor issues found but core functionality works.")
        elif success_rate >= 70:
            print("⚠️ Some issues found. Review and fix before production.")
        else:
            print("💥 Significant issues found. Major fixes needed.")
        
        return success_rate >= 85


def main():
    """Main test runner"""
    test_suite = CommunicationsTestSuite()
    success = test_suite.run_all_tests()
    
    if success:
        print("\n✨ All communication features tested successfully!")
        sys.exit(0)
    else:
        print("\n💥 Some tests failed. Please review errors above.")
        sys.exit(1)


if __name__ == '__main__':
    main()
