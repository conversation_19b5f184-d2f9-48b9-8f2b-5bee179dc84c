# HeartGrid Django Deployment Guide

## Quick Start (Development)

### 1. Environment Setup
```bash
# Activate virtual environment
venv\Scripts\activate  # Windows
source venv/bin/activate  # Linux/Mac

# Install dependencies (already done)
pip install -r requirements.txt
```

### 2. Database Setup
```bash
# Run migrations (already done)
python manage.py migrate

# Create superuser (optional)
python manage.py createsuperuser
```

### 3. Start Development Server
```bash
# Start Django development server
python manage.py runserver 8000

# Access application
# Frontend: http://127.0.0.1:8000
# Admin: http://127.0.0.1:8000/admin
# API: http://127.0.0.1:8000/api
```

## Production Deployment

### Environment Variables
Create a `.env` file with:
```env
# Django Settings
SECRET_KEY=your-secret-key-here
DEBUG=False
ALLOWED_HOSTS=yourdomain.com,www.yourdomain.com

# Database (PostgreSQL recommended)
DATABASE_URL=postgresql://user:password@localhost:5432/heartgrid

# NOWPayments Configuration
NOWPAYMENTS_API_KEY=your-nowpayments-api-key
NOWPAYMENTS_IPN_SECRET=your-ipn-secret-key

# Email Configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password
EMAIL_USE_TLS=True

# Social Authentication (Optional)
GOOGLE_OAUTH2_CLIENT_ID=your-google-client-id
GOOGLE_OAUTH2_CLIENT_SECRET=your-google-client-secret

# Security
SECURE_SSL_REDIRECT=True
SECURE_HSTS_SECONDS=31536000
SECURE_HSTS_INCLUDE_SUBDOMAINS=True
SECURE_HSTS_PRELOAD=True
```

### Database Migration (SQLite to PostgreSQL)

#### 1. Install PostgreSQL
```bash
# Ubuntu/Debian
sudo apt-get install postgresql postgresql-contrib

# Install Python PostgreSQL adapter
pip install psycopg2-binary
```

#### 2. Create PostgreSQL Database
```sql
-- Connect to PostgreSQL
sudo -u postgres psql

-- Create database and user
CREATE DATABASE heartgrid;
CREATE USER heartgrid_user WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE heartgrid TO heartgrid_user;
```

#### 3. Update Django Settings
```python
# heartgrid_django/settings.py
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': 'heartgrid',
        'USER': 'heartgrid_user',
        'PASSWORD': 'your_password',
        'HOST': 'localhost',
        'PORT': '5432',
    }
}
```

#### 4. Migrate Data
```bash
# Export data from SQLite
python manage.py dumpdata --natural-foreign --natural-primary > data.json

# Update database settings to PostgreSQL
# Run migrations
python manage.py migrate

# Import data
python manage.py loaddata data.json
```

### Web Server Configuration

#### Nginx Configuration
```nginx
# /etc/nginx/sites-available/heartgrid
server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;
    
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    location /static/ {
        alias /path/to/heartgrid/staticfiles/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    location /media/ {
        alias /path/to/heartgrid/media/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

#### Gunicorn Configuration
```bash
# Install Gunicorn
pip install gunicorn

# Create Gunicorn service file
# /etc/systemd/system/heartgrid.service
[Unit]
Description=HeartGrid Django Application
After=network.target

[Service]
User=www-data
Group=www-data
WorkingDirectory=/path/to/heartgrid
Environment="PATH=/path/to/heartgrid/venv/bin"
ExecStart=/path/to/heartgrid/venv/bin/gunicorn --workers 3 --bind 127.0.0.1:8000 heartgrid_django.wsgi:application
Restart=always

[Install]
WantedBy=multi-user.target
```

### SSL Certificate (Let's Encrypt)
```bash
# Install Certbot
sudo apt-get install certbot python3-certbot-nginx

# Get SSL certificate
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

### Static Files Configuration
```python
# heartgrid_django/settings.py
STATIC_URL = '/static/'
STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')
STATICFILES_DIRS = [
    os.path.join(BASE_DIR, 'static'),
]

# Media files
MEDIA_URL = '/media/'
MEDIA_ROOT = os.path.join(BASE_DIR, 'media')
```

```bash
# Collect static files
python manage.py collectstatic --noinput
```

## Docker Deployment (Alternative)

### Dockerfile
```dockerfile
FROM python:3.11-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY . .

RUN python manage.py collectstatic --noinput

EXPOSE 8000

CMD ["gunicorn", "--bind", "0.0.0.0:8000", "heartgrid_django.wsgi:application"]
```

### Docker Compose
```yaml
# docker-compose.yml
version: '3.8'

services:
  web:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DEBUG=False
      - DATABASE_URL=**************************************/heartgrid
    depends_on:
      - db
    volumes:
      - ./media:/app/media
      - ./staticfiles:/app/staticfiles

  db:
    image: postgres:13
    environment:
      POSTGRES_DB: heartgrid
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    volumes:
      - postgres_data:/var/lib/postgresql/data

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./staticfiles:/app/staticfiles
      - ./media:/app/media
    depends_on:
      - web

volumes:
  postgres_data:
```

## Monitoring & Maintenance

### Health Checks
```python
# Add to urls.py
path('health/', lambda request: JsonResponse({'status': 'ok'})),
```

### Logging Configuration
```python
# heartgrid_django/settings.py
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': '/var/log/heartgrid/django.log',
        },
    },
    'loggers': {
        'django': {
            'handlers': ['file'],
            'level': 'INFO',
            'propagate': True,
        },
    },
}
```

### Backup Strategy
```bash
# Database backup script
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
pg_dump heartgrid > /backups/heartgrid_$DATE.sql
find /backups -name "heartgrid_*.sql" -mtime +7 -delete
```

## Performance Optimization

### Database Optimization
```python
# Add database indexes
class Meta:
    indexes = [
        models.Index(fields=['created_at']),
        models.Index(fields=['user1', 'user2']),
    ]
```

### Caching
```python
# Install Redis
pip install django-redis

# settings.py
CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': 'redis://127.0.0.1:6379/1',
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
        }
    }
}
```

## Security Checklist

- [ ] Update SECRET_KEY for production
- [ ] Set DEBUG=False
- [ ] Configure ALLOWED_HOSTS
- [ ] Enable HTTPS with SSL certificate
- [ ] Set up CSRF protection
- [ ] Configure secure headers
- [ ] Regular security updates
- [ ] Database access restrictions
- [ ] API rate limiting
- [ ] Input validation

## Troubleshooting

### Common Issues
1. **Static files not loading**: Run `collectstatic`
2. **Database connection errors**: Check DATABASE_URL
3. **CSRF errors**: Ensure CSRF middleware is enabled
4. **Permission errors**: Check file/directory permissions
5. **Memory issues**: Increase server resources or optimize queries

### Debug Mode
```python
# Temporarily enable debug for troubleshooting
DEBUG = True
ALLOWED_HOSTS = ['*']  # Only for debugging
```

---

**Status**: ✅ Ready for production deployment  
**Estimated deployment time**: 2-4 hours  
**Recommended server**: 2GB RAM, 2 CPU cores minimum
