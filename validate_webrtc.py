#!/usr/bin/env python
"""
Simple validation script for HeartGrid WebRTC implementation
"""

import os
import json

def check_file_exists(file_path, description):
    """Check if a file exists and print result"""
    exists = os.path.exists(file_path)
    status = "✓" if exists else "✗"
    print(f"{status} {description}: {file_path}")
    return exists

def check_javascript_syntax(file_path):
    """Basic JavaScript syntax check"""
    try:
        with open(file_path, 'r') as f:
            content = f.read()
        
        # Basic checks
        has_class = 'class ' in content
        balanced_braces = content.count('{') == content.count('}')
        balanced_parens = content.count('(') == content.count(')')
        
        return has_class and balanced_braces and balanced_parens
    except:
        return False

def main():
    print("🚀 HeartGrid WebRTC Implementation Validation")
    print("=" * 50)
    
    # Check core files
    print("\n📁 Core Files:")
    files_ok = 0
    total_files = 0
    
    core_files = [
        ('communications/models.py', 'Database models'),
        ('communications/consumers.py', 'WebSocket consumers'),
        ('communications/notifications.py', 'Notification system'),
        ('communications/webrtc_config.py', 'WebRTC configuration'),
        ('communications/context_processors.py', 'Template context processors'),
        ('static/js/webrtc-manager.js', 'WebRTC JavaScript manager'),
        ('static/js/communications.js', 'Communications JavaScript'),
        ('static/css/webrtc-calling.css', 'WebRTC CSS styles'),
        ('templates/communications/webrtc_test.html', 'WebRTC test page')
    ]
    
    for file_path, description in core_files:
        if check_file_exists(file_path, description):
            files_ok += 1
        total_files += 1
    
    # Check JavaScript syntax
    print("\n🔧 JavaScript Syntax:")
    js_files = [
        'static/js/webrtc-manager.js',
        'static/js/communications.js'
    ]
    
    js_ok = 0
    for js_file in js_files:
        if os.path.exists(js_file):
            syntax_ok = check_javascript_syntax(js_file)
            status = "✓" if syntax_ok else "✗"
            print(f"{status} JavaScript syntax: {js_file}")
            if syntax_ok:
                js_ok += 1
    
    # Check CSS
    print("\n🎨 CSS Files:")
    css_file = 'static/css/webrtc-calling.css'
    css_ok = check_file_exists(css_file, 'WebRTC CSS styles')
    
    # Check management commands
    print("\n⚙️ Management Commands:")
    mgmt_files = [
        ('communications/management/commands/test_webrtc_config.py', 'WebRTC config test command')
    ]
    
    mgmt_ok = 0
    for file_path, description in mgmt_files:
        if check_file_exists(file_path, description):
            mgmt_ok += 1
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Validation Summary:")
    print(f"✅ Core files: {files_ok}/{total_files}")
    print(f"✅ JavaScript files: {js_ok}/{len(js_files)}")
    print(f"✅ CSS files: {1 if css_ok else 0}/1")
    print(f"✅ Management commands: {mgmt_ok}/{len(mgmt_files)}")
    
    total_score = files_ok + js_ok + (1 if css_ok else 0) + mgmt_ok
    max_score = total_files + len(js_files) + 1 + len(mgmt_files)
    
    success_rate = (total_score / max_score) * 100
    print(f"\n🎯 Overall Score: {total_score}/{max_score} ({success_rate:.1f}%)")
    
    if success_rate >= 90:
        print("🎉 Excellent! WebRTC implementation is complete.")
    elif success_rate >= 75:
        print("👍 Good! Minor files missing but core functionality is ready.")
    else:
        print("⚠️ Some important files are missing. Please review the implementation.")
    
    # Next steps
    print("\n🚀 Next Steps:")
    print("1. Run: python manage.py test_webrtc_config")
    print("2. Test WebRTC functionality in browser")
    print("3. Configure TURN servers for production")
    print("4. Run comprehensive tests")
    
    return success_rate >= 75

if __name__ == '__main__':
    success = main()
    exit(0 if success else 1)
