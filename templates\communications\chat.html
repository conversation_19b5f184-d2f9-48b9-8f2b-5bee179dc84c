{% extends "base_modern.html" %}
{% load static %}

{% block title %}Chat - HeartGrid{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/daisyui@4.12.14/dist/full.min.css" rel="stylesheet" type="text/css" />
<style>
    .chat-container {
        height: calc(100vh - 200px);
        display: flex;
        flex-direction: column;
    }
    
    .messages-container {
        flex: 1;
        overflow-y: auto;
        padding: 1rem;
        background: #f8fafc;
    }
    
    .message {
        margin-bottom: 1rem;
        display: flex;
    }
    
    .message.sent {
        justify-content: flex-end;
    }
    
    .message.received {
        justify-content: flex-start;
    }
    
    .message-content {
        max-width: 70%;
        padding: 0.75rem 1rem;
        border-radius: 1rem;
        position: relative;
    }
    
    .message.sent .message-content {
        background: #3b82f6;
        color: white;
        border-bottom-right-radius: 0.25rem;
    }
    
    .message.received .message-content {
        background: white;
        color: #1f2937;
        border-bottom-left-radius: 0.25rem;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }
    
    .message-text {
        margin-bottom: 0.25rem;
    }
    
    .message-meta {
        font-size: 0.75rem;
        opacity: 0.7;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .message-reactions {
        margin-top: 0.25rem;
    }
    
    .reaction {
        display: inline-block;
        background: rgba(255, 255, 255, 0.2);
        padding: 0.125rem 0.375rem;
        border-radius: 0.75rem;
        font-size: 0.75rem;
        margin-right: 0.25rem;
    }
    
    .typing-indicator {
        padding: 0.5rem 1rem;
        font-style: italic;
        color: #6b7280;
        display: none;
    }
    
    .message-input-container {
        padding: 1rem;
        background: white;
        border-top: 1px solid #e5e7eb;
    }
    
    .presence-indicator {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        display: inline-block;
        margin-left: 0.5rem;
    }
    
    .presence-indicator.online {
        background: #10b981;
    }
    
    .presence-indicator.away {
        background: #f59e0b;
    }
    
    .presence-indicator.offline {
        background: #6b7280;
    }
    
    .conversation-header {
        padding: 1rem;
        background: white;
        border-bottom: 1px solid #e5e7eb;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    
    .participant-info {
        display: flex;
        align-items: center;
    }
    
    .participant-name {
        font-weight: 600;
        margin-right: 0.5rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8" data-user-id="{{ user.id }}" data-user-name="{{ user.name }}">
    <div class="max-w-4xl mx-auto">
        <div class="card bg-base-100 shadow-xl">
            <div class="chat-container">
                <!-- Conversation Header -->
                <div class="conversation-header">
                    <div class="participant-info">
                        <h2 class="participant-name">Chat</h2>
                        <span class="presence-indicator offline" data-user-presence="other-user-id"></span>
                    </div>
                    <div class="flex gap-2">
                        <button class="btn btn-ghost btn-sm">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                            </svg>
                        </button>
                        <button class="btn btn-ghost btn-sm">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                            </svg>
                        </button>
                    </div>
                </div>
                
                <!-- Messages Container -->
                <div id="messages-container" class="messages-container">
                    <!-- Messages will be dynamically added here -->
                </div>
                
                <!-- Typing Indicator -->
                <div id="typing-indicator" class="typing-indicator">
                    Someone is typing...
                </div>
                
                <!-- Message Input -->
                <div class="message-input-container">
                    <div class="flex gap-2">
                        <div class="flex-1">
                            <textarea 
                                id="message-input" 
                                class="textarea textarea-bordered w-full resize-none" 
                                placeholder="Type your message..."
                                rows="1"
                            ></textarea>
                        </div>
                        <button id="send-button" class="btn btn-primary">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                            </svg>
                        </button>
                    </div>
                    
                    <!-- Emoji Reactions -->
                    <div class="flex gap-1 mt-2">
                        <button class="reaction-button btn btn-ghost btn-xs" data-emoji="❤️">❤️</button>
                        <button class="reaction-button btn btn-ghost btn-xs" data-emoji="😍">😍</button>
                        <button class="reaction-button btn btn-ghost btn-xs" data-emoji="😂">😂</button>
                        <button class="reaction-button btn btn-ghost btn-xs" data-emoji="👍">👍</button>
                        <button class="reaction-button btn btn-ghost btn-xs" data-emoji="😢">😢</button>
                        <button class="reaction-button btn btn-ghost btn-xs" data-emoji="😮">😮</button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Conversation List (Sidebar) -->
        <div class="mt-8">
            <div class="card bg-base-100 shadow-xl">
                <div class="card-body">
                    <h3 class="card-title">Conversations</h3>
                    <div id="conversations-list">
                        <!-- Conversations will be loaded here -->
                        <div class="text-center py-8 text-gray-500">
                            <p>No conversations yet</p>
                            <p class="text-sm">Start chatting with your matches!</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Notification Permission Modal -->
<div id="notification-modal" class="modal">
    <div class="modal-box">
        <h3 class="font-bold text-lg">Enable Notifications</h3>
        <p class="py-4">Allow HeartGrid to send you notifications for new messages?</p>
        <div class="modal-action">
            <button id="enable-notifications" class="btn btn-primary">Enable</button>
            <button id="skip-notifications" class="btn btn-ghost">Skip</button>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/communications.js' %}"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-resize textarea
    const messageInput = document.getElementById('message-input');
    if (messageInput) {
        messageInput.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = Math.min(this.scrollHeight, 120) + 'px';
        });
    }
    
    // Load conversations
    loadConversations();
    
    // Example: Open a conversation (you would get this from URL params or conversation list)
    // window.heartGridComms.openConversation('conversation-uuid-here');
});

async function loadConversations() {
    try {
        const response = await fetch('/api/communications/conversations/');
        if (response.ok) {
            const conversations = await response.json();
            displayConversations(conversations.results || conversations);
        }
    } catch (error) {
        console.error('Error loading conversations:', error);
    }
}

function displayConversations(conversations) {
    const container = document.getElementById('conversations-list');
    if (!conversations || conversations.length === 0) {
        return;
    }
    
    container.innerHTML = conversations.map(conv => `
        <div class="conversation-item p-3 hover:bg-gray-50 cursor-pointer border-b" 
             onclick="openConversation('${conv.id}')">
            <div class="flex justify-between items-start">
                <div class="flex-1">
                    <h4 class="font-semibold">${conv.other_participant?.name || 'Unknown'}</h4>
                    <p class="text-sm text-gray-600 truncate">
                        ${conv.last_message?.content || 'No messages yet'}
                    </p>
                </div>
                <div class="text-right">
                    <span class="text-xs text-gray-500">
                        ${conv.last_message?.created_at ? new Date(conv.last_message.created_at).toLocaleDateString() : ''}
                    </span>
                    ${conv.unread_count > 0 ? `<div class="badge badge-primary badge-sm">${conv.unread_count}</div>` : ''}
                </div>
            </div>
        </div>
    `).join('');
}

function openConversation(conversationId) {
    // Update URL or handle navigation
    window.history.pushState({}, '', `/chat/${conversationId}/`);
    
    // Open WebSocket connection for this conversation
    window.heartGridComms.openConversation(conversationId);
    
    // Load conversation messages
    loadConversationMessages(conversationId);
}

async function loadConversationMessages(conversationId) {
    try {
        const response = await fetch(`/api/communications/conversations/${conversationId}/messages/`);
        if (response.ok) {
            const data = await response.json();
            const messages = data.results || data;
            
            const container = document.getElementById('messages-container');
            container.innerHTML = '';
            
            messages.reverse().forEach(message => {
                window.heartGridComms.displayMessage(message);
            });
        }
    } catch (error) {
        console.error('Error loading messages:', error);
    }
}
</script>
{% endblock %}
