#!/usr/bin/env python
"""
Comprehensive integration test script for HeartGrid WebRTC calling system
Run this script to validate the complete WebRTC implementation
"""

import os
import sys
import django
import asyncio
import json
from datetime import datetime

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'heartgrid_django.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.test import TestCase
from communications.models import Conversation, CallSession, UserPresence, UserCallSettings
from communications.webrtc_config import WebRTCConfig
from communications.notifications import NotificationManager

User = get_user_model()


class WebRTCIntegrationValidator:
    """Comprehensive WebRTC integration validator"""
    
    def __init__(self):
        self.results = {
            'passed': 0,
            'failed': 0,
            'errors': []
        }
    
    def log_test(self, test_name, passed, error=None):
        """Log test result"""
        status = "✓ PASS" if passed else "✗ FAIL"
        print(f"{status}: {test_name}")
        
        if passed:
            self.results['passed'] += 1
        else:
            self.results['failed'] += 1
            if error:
                print(f"   Error: {error}")
                self.results['errors'].append(f"{test_name}: {error}")
    
    def test_database_models(self):
        """Test database models and relationships"""
        print("\n=== Testing Database Models ===")
        
        try:
            # Test User creation
            user1 = User.objects.create_user(
                username='test_caller',
                email='<EMAIL>',
                password='testpass123'
            )
            user2 = User.objects.create_user(
                username='test_callee',
                email='<EMAIL>',
                password='testpass123'
            )
            self.log_test("User model creation", True)
        except Exception as e:
            self.log_test("User model creation", False, str(e))
            return
        
        try:
            # Test Conversation creation
            conversation = Conversation.objects.create(name='Test Conversation')
            conversation.participants.add(user1, user2)
            self.log_test("Conversation model creation", True)
        except Exception as e:
            self.log_test("Conversation model creation", False, str(e))
            return
        
        try:
            # Test CallSession creation
            call_session = CallSession.objects.create(
                caller=user1,
                callee=user2,
                conversation=conversation,
                call_type='voice',
                call_state='initiating'
            )
            self.log_test("CallSession model creation", True)
        except Exception as e:
            self.log_test("CallSession model creation", False, str(e))
        
        try:
            # Test UserPresence creation
            presence = UserPresence.objects.create(
                user=user1,
                status='online',
                call_availability='available'
            )
            self.log_test("UserPresence model creation", True)
        except Exception as e:
            self.log_test("UserPresence model creation", False, str(e))
        
        try:
            # Test UserCallSettings creation
            settings = UserCallSettings.objects.create(
                user=user1,
                voice_calls_enabled=True,
                video_calls_enabled=True
            )
            self.log_test("UserCallSettings model creation", True)
        except Exception as e:
            self.log_test("UserCallSettings model creation", False, str(e))
        
        # Cleanup
        try:
            User.objects.filter(username__startswith='test_').delete()
            Conversation.objects.filter(name='Test Conversation').delete()
        except:
            pass
    
    def test_webrtc_configuration(self):
        """Test WebRTC configuration"""
        print("\n=== Testing WebRTC Configuration ===")
        
        try:
            config = WebRTCConfig.get_webrtc_configuration()
            self.log_test("WebRTC configuration retrieval", True)
            
            # Check ICE servers
            ice_servers = config.get('iceServers', [])
            has_stun = any(server['urls'].startswith('stun:') for server in ice_servers)
            self.log_test("STUN servers configured", has_stun)
            
            # Check configuration validation
            is_valid, errors = WebRTCConfig.validate_configuration()
            self.log_test("Configuration validation", is_valid, '; '.join(errors) if errors else None)
            
        except Exception as e:
            self.log_test("WebRTC configuration", False, str(e))
    
    def test_media_constraints(self):
        """Test media constraints configuration"""
        print("\n=== Testing Media Constraints ===")
        
        try:
            constraints = WebRTCConfig.get_media_constraints()
            
            # Check audio constraints
            audio = constraints.get('audio', {})
            has_audio_settings = all(key in audio for key in ['echoCancellation', 'noiseSuppression'])
            self.log_test("Audio constraints configured", has_audio_settings)
            
            # Check video constraints
            video = constraints.get('video', {})
            has_video_settings = all(key in video for key in ['width', 'height'])
            self.log_test("Video constraints configured", has_video_settings)
            
        except Exception as e:
            self.log_test("Media constraints", False, str(e))
    
    def test_notification_system(self):
        """Test notification system"""
        print("\n=== Testing Notification System ===")
        
        try:
            # Create test users
            user1 = User.objects.create_user(
                username='notif_caller',
                email='<EMAIL>',
                password='testpass123'
            )
            user2 = User.objects.create_user(
                username='notif_callee',
                email='<EMAIL>',
                password='testpass123'
            )
            
            # Test notification manager
            notification_manager = NotificationManager()
            self.log_test("NotificationManager instantiation", True)
            
            # Test call availability notification
            notification_manager.send_call_availability_notification(user1, 'busy')
            self.log_test("Call availability notification", True)
            
            # Cleanup
            User.objects.filter(username__startswith='notif_').delete()
            
        except Exception as e:
            self.log_test("Notification system", False, str(e))
    
    def test_file_structure(self):
        """Test required files exist"""
        print("\n=== Testing File Structure ===")
        
        required_files = [
            'communications/models.py',
            'communications/consumers.py',
            'communications/notifications.py',
            'communications/webrtc_config.py',
            'static/js/webrtc-manager.js',
            'static/js/communications.js',
            'static/css/webrtc-calling.css',
            'templates/communications/webrtc_test.html'
        ]
        
        for file_path in required_files:
            exists = os.path.exists(file_path)
            self.log_test(f"File exists: {file_path}", exists)
    
    def test_javascript_syntax(self):
        """Test JavaScript files for basic syntax"""
        print("\n=== Testing JavaScript Syntax ===")
        
        js_files = [
            'static/js/webrtc-manager.js',
            'static/js/communications.js'
        ]
        
        for js_file in js_files:
            try:
                if os.path.exists(js_file):
                    with open(js_file, 'r') as f:
                        content = f.read()
                    
                    # Basic syntax checks
                    has_class = 'class ' in content
                    has_constructor = 'constructor(' in content
                    balanced_braces = content.count('{') == content.count('}')
                    
                    syntax_ok = has_class and has_constructor and balanced_braces
                    self.log_test(f"JavaScript syntax: {js_file}", syntax_ok)
                else:
                    self.log_test(f"JavaScript file exists: {js_file}", False)
            except Exception as e:
                self.log_test(f"JavaScript syntax: {js_file}", False, str(e))
    
    def test_css_syntax(self):
        """Test CSS files for basic syntax"""
        print("\n=== Testing CSS Syntax ===")
        
        css_file = 'static/css/webrtc-calling.css'
        try:
            if os.path.exists(css_file):
                with open(css_file, 'r') as f:
                    content = f.read()
                
                # Basic CSS syntax checks
                has_selectors = '.' in content or '#' in content
                balanced_braces = content.count('{') == content.count('}')
                
                syntax_ok = has_selectors and balanced_braces
                self.log_test(f"CSS syntax: {css_file}", syntax_ok)
            else:
                self.log_test(f"CSS file exists: {css_file}", False)
        except Exception as e:
            self.log_test(f"CSS syntax: {css_file}", False, str(e))
    
    def run_all_tests(self):
        """Run all integration tests"""
        print("🚀 Starting HeartGrid WebRTC Integration Tests")
        print("=" * 60)
        
        self.test_database_models()
        self.test_webrtc_configuration()
        self.test_media_constraints()
        self.test_notification_system()
        self.test_file_structure()
        self.test_javascript_syntax()
        self.test_css_syntax()
        
        # Print summary
        print("\n" + "=" * 60)
        print("🏁 Test Summary")
        print("=" * 60)
        print(f"✅ Passed: {self.results['passed']}")
        print(f"❌ Failed: {self.results['failed']}")
        
        if self.results['errors']:
            print("\n🔍 Error Details:")
            for error in self.results['errors']:
                print(f"   • {error}")
        
        success_rate = (self.results['passed'] / (self.results['passed'] + self.results['failed'])) * 100
        print(f"\n📊 Success Rate: {success_rate:.1f}%")
        
        if success_rate >= 90:
            print("🎉 Excellent! WebRTC implementation is ready for production.")
        elif success_rate >= 75:
            print("👍 Good! Minor issues to address before production.")
        else:
            print("⚠️  Significant issues found. Review and fix before deployment.")
        
        return success_rate >= 75


def main():
    """Main test runner"""
    validator = WebRTCIntegrationValidator()
    success = validator.run_all_tests()
    
    if success:
        print("\n✨ Integration tests completed successfully!")
        sys.exit(0)
    else:
        print("\n💥 Integration tests failed. Please review errors above.")
        sys.exit(1)


if __name__ == '__main__':
    main()
