{% extends "base.html" %}

{% block title %}Crypto Subscription - HeartGrid{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="text-center mb-5">
                <h1 class="display-5 fw-bold text-primary">Choose Your Plan</h1>
                <p class="lead text-muted">Pay with cryptocurrency on your preferred blockchain</p>
            </div>

            <!-- Pricing Cards -->
            <div class="row g-4 mb-5">
                <div class="col-md-4">
                    <div class="card border-0 shadow-sm h-100 plan-card" data-plan="weekly">
                        <div class="card-body text-center p-4">
                            <h5 class="card-title text-primary">Weekly</h5>
                            <div class="price-display">
                                <span class="h2 fw-bold">$5.00</span>
                                <small class="text-muted d-block">per week</small>
                            </div>
                            <ul class="list-unstyled mt-3 mb-4">
                                <li><i class="fas fa-check text-success me-2"></i>Unlimited messaging</li>
                                <li><i class="fas fa-check text-success me-2"></i>See who liked you</li>
                                <li><i class="fas fa-check text-success me-2"></i>Voice & video calls</li>
                            </ul>
                            <button class="btn btn-outline-primary w-100 select-plan-btn">Select Plan</button>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card border-primary shadow h-100 plan-card popular" data-plan="fortnightly">
                        <div class="card-header bg-primary text-white text-center">
                            <small class="fw-bold">MOST POPULAR</small>
                        </div>
                        <div class="card-body text-center p-4">
                            <h5 class="card-title text-primary">Fortnightly</h5>
                            <div class="price-display">
                                <span class="h2 fw-bold">$9.99</span>
                                <small class="text-muted d-block">per 2 weeks</small>
                            </div>
                            <ul class="list-unstyled mt-3 mb-4">
                                <li><i class="fas fa-check text-success me-2"></i>Unlimited messaging</li>
                                <li><i class="fas fa-check text-success me-2"></i>See who liked you</li>
                                <li><i class="fas fa-check text-success me-2"></i>Voice & video calls</li>
                                <li><i class="fas fa-check text-success me-2"></i>Priority support</li>
                            </ul>
                            <button class="btn btn-primary w-100 select-plan-btn">Select Plan</button>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card border-0 shadow-sm h-100 plan-card" data-plan="monthly">
                        <div class="card-body text-center p-4">
                            <h5 class="card-title text-primary">Monthly</h5>
                            <div class="price-display">
                                <span class="h2 fw-bold">$19.99</span>
                                <small class="text-muted d-block">per month</small>
                                <small class="badge bg-success">Best Value</small>
                            </div>
                            <ul class="list-unstyled mt-3 mb-4">
                                <li><i class="fas fa-check text-success me-2"></i>Everything in Fortnightly</li>
                                <li><i class="fas fa-check text-success me-2"></i>Advanced filters</li>
                                <li><i class="fas fa-check text-success me-2"></i>Read receipts</li>
                                <li><i class="fas fa-check text-success me-2"></i>VIP profile boost</li>
                            </ul>
                            <button class="btn btn-outline-primary w-100 select-plan-btn">Select Plan</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payment Method Selection -->
            <div id="paymentSection" class="card shadow-sm" style="display: none;">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-coins me-2"></i>
                        Select Payment Method
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3" id="networkSelection">
                        <!-- Networks will be populated by JavaScript -->
                    </div>
                </div>
            </div>

            <!-- Payment Details -->
            <div id="paymentDetails" class="card shadow-sm mt-4" style="display: none;">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-wallet me-2"></i>
                        Payment Details
                    </h5>
                    <button class="btn btn-sm btn-outline-secondary" onclick="goBack()">
                        <i class="fas fa-arrow-left me-1"></i>Back
                    </button>
                </div>
                <div class="card-body" id="paymentContent">
                    <!-- Payment content will be loaded here -->
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.plan-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    cursor: pointer;
}

.plan-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15) !important;
}

.plan-card.selected {
    border-color: #0d6efd !important;
    box-shadow: 0 0 0 3px rgba(13, 110, 253, 0.25);
}

.plan-card.popular {
    transform: scale(1.05);
    position: relative;
    z-index: 1;
}

.network-card {
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
}

.network-card:hover {
    border-color: #0d6efd;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.network-card.selected {
    border-color: #0d6efd;
    background: #f8f9ff;
}

.network-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 12px;
    color: white;
    font-size: 20px;
}

.token-selector {
    display: flex;
    gap: 10px;
    justify-content: center;
    margin-top: 15px;
}

.token-btn {
    padding: 8px 16px;
    border: 2px solid #e9ecef;
    border-radius: 20px;
    background: white;
    cursor: pointer;
    font-size: 12px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.token-btn:hover, .token-btn.selected {
    border-color: #0d6efd;
    background: #0d6efd;
    color: white;
}

.payment-address {
    font-family: 'Courier New', monospace;
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border: 1px solid #dee2e6;
    word-break: break-all;
    font-size: 14px;
}

.qr-container {
    text-align: center;
    padding: 20px;
    background: white;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.amount-display {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 12px;
    text-align: center;
    margin-bottom: 20px;
}
</style>

<script>
let selectedPlan = null;
let selectedNetwork = null;
let selectedToken = null;
let currentPayment = null;

// Supported networks configuration
const networks = [
    {
        id: 'ethereum',
        name: 'Ethereum',
        symbol: 'ETH',
        icon: 'fab fa-ethereum',
        color: '#627eea'
    },
    {
        id: 'bsc',
        name: 'BNB Smart Chain',
        symbol: 'BNB',
        icon: 'fas fa-coins',
        color: '#f3ba2f'
    },
    {
        id: 'solana',
        name: 'Solana',
        symbol: 'SOL',
        icon: 'fas fa-sun',
        color: '#14f195'
    },
    {
        id: 'tron',
        name: 'Tron',
        symbol: 'TRX',
        icon: 'fas fa-bolt',
        color: '#ff060a'
    },
    {
        id: 'ton',
        name: 'TON',
        symbol: 'TON',
        icon: 'fas fa-gem',
        color: '#0088cc'
    }
];

document.addEventListener('DOMContentLoaded', function() {
    initializePlanSelection();
    populateNetworks();
});

function initializePlanSelection() {
    document.querySelectorAll('.select-plan-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const planCard = this.closest('.plan-card');
            selectedPlan = planCard.dataset.plan;
            
            // Update UI
            document.querySelectorAll('.plan-card').forEach(card => {
                card.classList.remove('selected');
            });
            planCard.classList.add('selected');
            
            // Show payment section
            document.getElementById('paymentSection').style.display = 'block';
            document.getElementById('paymentSection').scrollIntoView({ behavior: 'smooth' });
        });
    });
}

function populateNetworks() {
    const container = document.getElementById('networkSelection');
    
    networks.forEach(network => {
        const networkHtml = `
            <div class="col-md-4 col-lg-2">
                <div class="network-card" onclick="selectNetwork('${network.id}')">
                    <div class="network-icon" style="background: ${network.color}">
                        <i class="${network.icon}"></i>
                    </div>
                    <h6 class="fw-bold mb-1">${network.name}</h6>
                    <small class="text-muted">${network.symbol}</small>
                    <div class="token-selector">
                        <div class="token-btn" onclick="selectPayment('${network.id}', 'native', event)">${network.symbol}</div>
                        <div class="token-btn" onclick="selectPayment('${network.id}', 'USDT', event)">USDT</div>
                    </div>
                </div>
            </div>
        `;
        container.innerHTML += networkHtml;
    });
}

function selectNetwork(networkId) {
    selectedNetwork = networkId;
    
    // Update UI
    document.querySelectorAll('.network-card').forEach(card => {
        card.classList.remove('selected');
    });
    event.currentTarget.classList.add('selected');
}

function selectPayment(networkId, tokenType, event) {
    event.stopPropagation();
    
    if (!selectedPlan) {
        showNotification('Please select a plan first', 'warning');
        return;
    }
    
    selectedNetwork = networkId;
    selectedToken = tokenType;
    
    // Update network selection
    document.querySelectorAll('.network-card').forEach(card => {
        card.classList.remove('selected');
    });
    event.currentTarget.closest('.network-card').classList.add('selected');
    
    // Update token selection
    event.currentTarget.closest('.network-card').querySelectorAll('.token-btn').forEach(btn => {
        btn.classList.remove('selected');
    });
    event.currentTarget.classList.add('selected');
    
    // Create payment request
    createPaymentRequest();
}

function createPaymentRequest() {
    showLoading();
    
    fetch('/create_crypto_payment', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            plan: selectedPlan,
            network: selectedNetwork,
            token: selectedToken
        })
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        if (data.success) {
            currentPayment = data.payment;
            displayPaymentDetails(data.payment);
        } else {
            showNotification(data.error || 'Failed to create payment request', 'error');
        }
    })
    .catch(error => {
        hideLoading();
        showNotification('Network error. Please try again.', 'error');
        console.error('Error:', error);
    });
}

function displayPaymentDetails(payment) {
    const content = `
        <div class="amount-display">
            <h4 class="mb-1">Send Exactly</h4>
            <div class="h2 fw-bold">${payment.amount} ${payment.symbol}</div>
            <small>≈ $${payment.usd_value} USD</small>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <h6 class="fw-bold mb-3">Payment Address</h6>
                <div class="payment-address mb-3">
                    ${payment.address}
                </div>
                <button class="btn btn-outline-primary btn-sm w-100" onclick="copyAddress('${payment.address}')">
                    <i class="fas fa-copy me-1"></i>Copy Address
                </button>
                
                ${payment.token_type === 'USDT' ? `
                <div class="mt-3">
                    <h6 class="fw-bold">Contract Address</h6>
                    <small class="text-muted d-block">${payment.chain_info.usdt_contract}</small>
                </div>
                ` : ''}
            </div>
            
            <div class="col-md-6">
                <div class="qr-container">
                    <h6 class="fw-bold mb-3">QR Code</h6>
                    <div id="qrcode" class="mb-3"></div>
                    <small class="text-muted">Scan with your wallet app</small>
                </div>
            </div>
        </div>
        
        <div class="alert alert-warning mt-4">
            <h6 class="alert-heading">
                <i class="fas fa-exclamation-triangle me-2"></i>Important Instructions
            </h6>
            <ul class="mb-0">
                ${payment.instructions.steps.map(step => `<li>${step}</li>`).join('')}
            </ul>
        </div>
        
        <div class="mt-4 text-center">
            <button class="btn btn-success btn-lg" onclick="checkPaymentStatus()">
                <i class="fas fa-check me-2"></i>I've Sent the Payment
            </button>
            <p class="text-muted mt-2 small">
                Payment expires in <span id="countdown">24:00:00</span>
            </p>
        </div>
        
        <div class="mt-3">
            <small class="text-muted">
                Payment ID: ${payment.payment_id}<br>
                Network: ${payment.chain_info.name}<br>
                Explorer: <a href="${payment.chain_info.explorer}" target="_blank">View on ${payment.chain_info.name} Explorer</a>
            </small>
        </div>
    `;
    
    document.getElementById('paymentContent').innerHTML = content;
    document.getElementById('paymentDetails').style.display = 'block';
    document.getElementById('paymentDetails').scrollIntoView({ behavior: 'smooth' });
    
    // Generate QR code
    generateQRCode(payment.qr_data);
    
    // Start countdown timer
    startCountdown(payment.expires_at);
    
    // Start checking payment status
    startPaymentStatusCheck();
}

function generateQRCode(data) {
    // Using QRCode.js library (would need to include it)
    const qrContainer = document.getElementById('qrcode');
    qrContainer.innerHTML = `
        <div class="bg-light p-4 rounded">
            <i class="fas fa-qrcode fa-3x text-muted"></i>
            <p class="mt-2 mb-0 small text-muted">QR Code would appear here<br>
            (Requires QR library integration)</p>
        </div>
    `;
}

function copyAddress(address) {
    navigator.clipboard.writeText(address).then(() => {
        showNotification('Address copied to clipboard!', 'success');
    }).catch(() => {
        showNotification('Failed to copy address', 'error');
    });
}

function startCountdown(expiresAt) {
    const countdownElement = document.getElementById('countdown');
    const expiryTime = new Date(expiresAt).getTime();
    
    const timer = setInterval(() => {
        const now = new Date().getTime();
        const timeLeft = expiryTime - now;
        
        if (timeLeft <= 0) {
            clearInterval(timer);
            countdownElement.textContent = 'EXPIRED';
            showNotification('Payment request has expired. Please create a new one.', 'warning');
            return;
        }
        
        const hours = Math.floor(timeLeft / (1000 * 60 * 60));
        const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);
        
        countdownElement.textContent = 
            `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }, 1000);
}

function startPaymentStatusCheck() {
    if (!currentPayment) return;
    
    const checkStatus = () => {
        fetch('/check_crypto_payment', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                payment_id: currentPayment.payment_id
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'confirmed') {
                showNotification('Payment confirmed! Activating your subscription...', 'success');
                setTimeout(() => {
                    window.location.href = '/subscription';
                }, 2000);
            } else if (data.status === 'pending') {
                // Continue checking
                setTimeout(checkStatus, 30000); // Check every 30 seconds
            }
        })
        .catch(error => {
            console.error('Error checking payment status:', error);
            setTimeout(checkStatus, 60000); // Check again in 1 minute on error
        });
    };
    
    // Start checking after 1 minute
    setTimeout(checkStatus, 60000);
}

function checkPaymentStatus() {
    if (!currentPayment) return;
    
    showLoading();
    
    fetch('/check_crypto_payment', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            payment_id: currentPayment.payment_id
        })
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        if (data.status === 'confirmed') {
            showNotification('Payment confirmed! Redirecting...', 'success');
            setTimeout(() => {
                window.location.href = '/subscription';
            }, 2000);
        } else {
            showNotification('Payment not yet confirmed. Please wait for blockchain confirmation.', 'info');
        }
    })
    .catch(error => {
        hideLoading();
        showNotification('Error checking payment status', 'error');
    });
}

function goBack() {
    document.getElementById('paymentDetails').style.display = 'none';
    currentPayment = null;
}

function showLoading() {
    // Add loading spinner
    document.body.style.cursor = 'wait';
}

function hideLoading() {
    document.body.style.cursor = 'default';
}
</script>
{% endblock %}