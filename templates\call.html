{% extends "base.html" %}

{% block title %}Video Call - HeartGrid{% endblock %}

{% block content %}
<div class="video-call-container">
    <div class="call-header">
        <div class="call-info">
            <div class="caller-avatar">
                {% if match_user.photos %}
                    <img src="{{ url_for('uploaded_file', filename=match_user.photos[0]) }}" alt="{{ match_user.name }}">
                {% else %}
                    <div class="avatar-placeholder">{{ match_user.get('name', 'User')[0].upper() }}</div>
                {% endif %}
            </div>
            <div class="caller-details">
                <h3>{{ match_user.get('name', 'User') }}</h3>
                <p class="call-status">Connecting...</p>
            </div>
        </div>
        <div class="call-actions">
            <button class="btn btn-secondary" onclick="endCall()">
                <i class="fas fa-times"></i>
            </button>
        </div>
    </div>

    <div class="video-container">
        <div class="remote-video">
            <video id="remoteVideo" autoplay></video>
            <div class="video-placeholder" id="remotePlaceholder">
                <div class="avatar-large">
                    {% if match_user.photos %}
                        <img src="{{ url_for('uploaded_file', filename=match_user.photos[0]) }}" alt="{{ match_user.name }}">
                    {% else %}
                        <div class="avatar-placeholder-large">{{ match_user.get('name', 'User')[0].upper() }}</div>
                    {% endif %}
                </div>
                <p>{{ match_user.get('name', 'User') }}</p>
            </div>
        </div>
        
        <div class="local-video">
            <video id="localVideo" autoplay muted></video>
            <div class="video-controls-overlay">
                <button class="control-btn" id="muteBtn" onclick="toggleMute()">
                    <i class="fas fa-microphone"></i>
                </button>
                <button class="control-btn" id="videoBtn" onclick="toggleVideo()">
                    <i class="fas fa-video"></i>
                </button>
            </div>
        </div>
    </div>

    <div class="call-controls">
        <div class="control-group">
            <button class="control-btn secondary" onclick="toggleMute()" id="mainMuteBtn">
                <i class="fas fa-microphone"></i>
            </button>
            <button class="control-btn secondary" onclick="toggleVideo()" id="mainVideoBtn">
                <i class="fas fa-video"></i>
            </button>
            <button class="control-btn secondary" onclick="toggleSpeaker()">
                <i class="fas fa-volume-up"></i>
            </button>
        </div>
        
        <button class="control-btn danger" onclick="endCall()">
            <i class="fas fa-phone-slash"></i>
        </button>
        
        <div class="control-group">
            <button class="control-btn secondary" onclick="switchCamera()">
                <i class="fas fa-sync-alt"></i>
            </button>
            <button class="control-btn secondary" onclick="openChat()">
                <i class="fas fa-comment"></i>
            </button>
        </div>
    </div>
</div>

<style>
.video-call-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: #000;
    z-index: 9999;
    display: flex;
    flex-direction: column;
}

.call-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background: rgba(0,0,0,0.8);
    color: white;
}

.call-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.caller-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    overflow: hidden;
}

.caller-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.avatar-placeholder {
    width: 50px;
    height: 50px;
    background: #6c757d;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 18px;
    border-radius: 50%;
}

.caller-details h3 {
    margin: 0;
    font-size: 18px;
}

.call-status {
    margin: 0;
    color: #28a745;
    font-size: 14px;
}

.video-container {
    flex: 1;
    position: relative;
    display: flex;
}

.remote-video {
    flex: 1;
    position: relative;
    background: #1a1a1a;
}

.remote-video video {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.video-placeholder {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: white;
}

.avatar-large {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    overflow: hidden;
    margin: 0 auto 15px;
}

.avatar-large img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.avatar-placeholder-large {
    width: 120px;
    height: 120px;
    background: #6c757d;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 48px;
    border-radius: 50%;
}

.local-video {
    position: absolute;
    top: 20px;
    right: 20px;
    width: 200px;
    height: 150px;
    border-radius: 10px;
    overflow: hidden;
    border: 2px solid #fff;
    background: #333;
}

.local-video video {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.video-controls-overlay {
    position: absolute;
    bottom: 5px;
    right: 5px;
    display: flex;
    gap: 5px;
}

.call-controls {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 30px;
    background: rgba(0,0,0,0.8);
    gap: 20px;
}

.control-group {
    display: flex;
    gap: 15px;
}

.control-btn {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.control-btn.secondary {
    background: rgba(255,255,255,0.2);
    color: white;
}

.control-btn.secondary:hover {
    background: rgba(255,255,255,0.3);
}

.control-btn.danger {
    background: #dc3545;
    color: white;
    width: 80px;
    height: 80px;
    font-size: 24px;
}

.control-btn.danger:hover {
    background: #c82333;
    transform: scale(1.1);
}

.control-btn.muted {
    background: #dc3545;
}

@media (max-width: 768px) {
    .local-video {
        width: 120px;
        height: 90px;
        top: 10px;
        right: 10px;
    }
    
    .control-btn {
        width: 50px;
        height: 50px;
        font-size: 16px;
    }
    
    .control-btn.danger {
        width: 70px;
        height: 70px;
        font-size: 20px;
    }
    
    .call-controls {
        padding: 20px;
        gap: 15px;
    }
    
    .control-group {
        gap: 10px;
    }
}
</style>

<script>
let localStream = null;
let remoteStream = null;
let isMuted = false;
let isVideoOff = false;

// Initialize call on page load
document.addEventListener('DOMContentLoaded', function() {
    initializeCall();
});

async function initializeCall() {
    try {
        // Get user media
        localStream = await navigator.mediaDevices.getUserMedia({ 
            video: true, 
            audio: true 
        });
        
        document.getElementById('localVideo').srcObject = localStream;
        updateCallStatus('Connected');
        
        // In a real implementation, you would use WebRTC with signaling server
        // For demo purposes, we'll simulate a call
        setTimeout(() => {
            updateCallStatus('Call in progress');
        }, 2000);
        
    } catch (error) {
        console.error('Error accessing media devices:', error);
        updateCallStatus('Failed to access camera/microphone');
        showNotification('Unable to access camera or microphone. Please check permissions.', 'error');
    }
}

function updateCallStatus(status) {
    document.querySelector('.call-status').textContent = status;
}

function toggleMute() {
    if (localStream) {
        const audioTrack = localStream.getAudioTracks()[0];
        if (audioTrack) {
            audioTrack.enabled = !audioTrack.enabled;
            isMuted = !audioTrack.enabled;
            
            const muteButtons = document.querySelectorAll('#muteBtn, #mainMuteBtn');
            muteButtons.forEach(btn => {
                btn.classList.toggle('muted', isMuted);
                btn.querySelector('i').className = isMuted ? 'fas fa-microphone-slash' : 'fas fa-microphone';
            });
        }
    }
}

function toggleVideo() {
    if (localStream) {
        const videoTrack = localStream.getVideoTracks()[0];
        if (videoTrack) {
            videoTrack.enabled = !videoTrack.enabled;
            isVideoOff = !videoTrack.enabled;
            
            const videoButtons = document.querySelectorAll('#videoBtn, #mainVideoBtn');
            videoButtons.forEach(btn => {
                btn.classList.toggle('muted', isVideoOff);
                btn.querySelector('i').className = isVideoOff ? 'fas fa-video-slash' : 'fas fa-video';
            });
        }
    }
}

function toggleSpeaker() {
    // Toggle speaker functionality
    showNotification('Speaker toggled', 'info');
}

function switchCamera() {
    // Switch between front and back camera
    showNotification('Camera switched', 'info');
}

function openChat() {
    // Open chat overlay during call
    showNotification('Chat feature coming soon!', 'info');
}

function endCall() {
    if (confirm('Are you sure you want to end the call?')) {
        // Stop all media streams
        if (localStream) {
            localStream.getTracks().forEach(track => track.stop());
        }
        if (remoteStream) {
            remoteStream.getTracks().forEach(track => track.stop());
        }
        
        // Redirect back to matches or chat
        window.location.href = "{{ url_for('matches') }}";
    }
}

// Handle browser back button
window.addEventListener('beforeunload', function(e) {
    if (localStream) {
        localStream.getTracks().forEach(track => track.stop());
    }
});
</script>
{% endblock %}