<!DOCTYPE html>
<html lang="en" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}HeartGrid - Modern Dating Platform{% endblock %}</title>
    
    <!-- Tailwind CSS with DaisyUI -->
    <link href="https://cdn.jsdelivr.net/npm/daisyui@4.4.19/dist/full.min.css" rel="stylesheet" type="text/css" />
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .card-hover {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        
        .profile-card {
            background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
            border: 1px solid rgba(226, 232, 240, 0.8);
        }
        
        .message-bubble {
            max-width: 70%;
            word-wrap: break-word;
        }
        
        .online-indicator {
            width: 12px;
            height: 12px;
            background: #10b981;
            border: 2px solid white;
            border-radius: 50%;
            position: absolute;
            bottom: 2px;
            right: 2px;
        }
        
        .match-animation {
            animation: heartbeat 1.5s ease-in-out infinite;
        }
        
        @keyframes heartbeat {
            0% { transform: scale(1); }
            14% { transform: scale(1.1); }
            28% { transform: scale(1); }
            42% { transform: scale(1.1); }
            70% { transform: scale(1); }
        }
        
        .swipe-card {
            touch-action: pan-y;
            user-select: none;
        }
        
        .notification-badge {
            position: absolute;
            top: -8px;
            right: -8px;
            background: #ef4444;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body class="bg-base-100 min-h-screen">
    <!-- Navigation -->
    <div class="navbar bg-base-100 shadow-lg sticky top-0 z-50">
        <div class="navbar-start">
            <div class="dropdown">
                <div tabindex="0" role="button" class="btn btn-ghost lg:hidden">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h8m-8 6h16"></path>
                    </svg>
                </div>
                <ul tabindex="0" class="menu menu-sm dropdown-content mt-3 z-[1] p-2 shadow bg-base-100 rounded-box w-52">
                    <li><a href="{% url 'discover' %}"><i class="fas fa-heart mr-2"></i>Discover</a></li>
                    <li><a href="{% url 'matches' %}"><i class="fas fa-users mr-2"></i>Matches</a></li>
                    <li><a href="{% url 'messages' %}"><i class="fas fa-comments mr-2"></i>Messages</a></li>
                    <li><a href="{% url 'profile' %}"><i class="fas fa-user mr-2"></i>Profile</a></li>
                </ul>
            </div>
            <a href="{% url 'index' %}" class="btn btn-ghost text-xl font-bold text-primary">
                <i class="fas fa-heart text-red-500 mr-2"></i>
                HeartGrid
            </a>
        </div>
        
        <div class="navbar-center hidden lg:flex">
            <ul class="menu menu-horizontal px-1">
                <li><a href="{% url 'discover' %}" class="btn btn-ghost"><i class="fas fa-heart mr-2"></i>Discover</a></li>
                <li><a href="{% url 'matches' %}" class="btn btn-ghost"><i class="fas fa-users mr-2"></i>Matches</a></li>
                <li><a href="{% url 'messages' %}" class="btn btn-ghost"><i class="fas fa-comments mr-2"></i>Messages</a></li>
                <li><a href="{% url 'profile' %}" class="btn btn-ghost"><i class="fas fa-user mr-2"></i>Profile</a></li>
            </ul>
        </div>
        
        <div class="navbar-end">
            {% if user.is_authenticated %}
                <!-- Notifications -->
                <div class="dropdown dropdown-end">
                    <div tabindex="0" role="button" class="btn btn-ghost btn-circle relative">
                        <i class="fas fa-bell text-lg"></i>
                        <div class="notification-badge" id="notification-count" style="display: none;">0</div>
                    </div>
                    <div tabindex="0" class="dropdown-content z-[1] card card-compact w-80 p-2 shadow bg-base-100">
                        <div class="card-body">
                            <h3 class="font-bold text-lg">Notifications</h3>
                            <div id="notifications-list" class="space-y-2">
                                <!-- Notifications will be loaded here -->
                            </div>
                            <div class="card-actions">
                                <button class="btn btn-primary btn-block btn-sm">View All</button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- User Menu -->
                <div class="dropdown dropdown-end">
                    <div tabindex="0" role="button" class="btn btn-ghost btn-circle avatar">
                        <div class="w-10 rounded-full">
                            <img alt="Profile" src="{% if user.profile.photos.exists %}{{ user.profile.photos.first.image.url }}{% else %}/static/images/default-avatar.png{% endif %}" />
                        </div>
                    </div>
                    <ul tabindex="0" class="menu menu-sm dropdown-content mt-3 z-[1] p-2 shadow bg-base-100 rounded-box w-52">
                        <li>
                            <a href="{% url 'profile' %}" class="justify-between">
                                Profile
                                <span class="badge">{{ user.profile.is_complete|yesno:"Complete,Incomplete" }}</span>
                            </a>
                        </li>
                        <li><a href="{% url 'settings' %}"><i class="fas fa-cog mr-2"></i>Settings</a></li>
                        <li><a href="{% url 'subscription' %}"><i class="fas fa-crown mr-2 text-yellow-500"></i>Premium</a></li>
                        <li><a href="{% url 'achievements' %}"><i class="fas fa-trophy mr-2 text-yellow-600"></i>Achievements</a></li>
                        <li><hr></li>
                        <li><a href="{% url 'logout' %}"><i class="fas fa-sign-out-alt mr-2"></i>Logout</a></li>
                    </ul>
                </div>
            {% else %}
                <div class="space-x-2">
                    <a href="{% url 'login' %}" class="btn btn-ghost">Login</a>
                    <a href="{% url 'register' %}" class="btn btn-primary">Sign Up</a>
                </div>
            {% endif %}
        </div>
    </div>

    <!-- Main Content -->
    <main class="container mx-auto px-4 py-6">
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="footer footer-center p-10 bg-base-200 text-base-content rounded mt-20">
        <nav class="grid grid-flow-col gap-4">
            <a href="/about/" class="link link-hover">About</a>
            <a href="/privacy/" class="link link-hover">Privacy</a>
            <a href="/terms/" class="link link-hover">Terms</a>
            <a href="/contact/" class="link link-hover">Contact</a>
        </nav>
        <nav>
            <div class="grid grid-flow-col gap-4">
                <a href="#" class="text-2xl hover:text-primary"><i class="fab fa-twitter"></i></a>
                <a href="#" class="text-2xl hover:text-primary"><i class="fab fa-instagram"></i></a>
                <a href="#" class="text-2xl hover:text-primary"><i class="fab fa-facebook"></i></a>
            </div>
        </nav>
        <aside>
            <p>Copyright © 2024 - HeartGrid Dating Platform. Made with ❤️ for modern connections.</p>
        </aside>
    </footer>

    <!-- Toast Container -->
    <div class="toast toast-top toast-end z-50" id="toast-container">
        <!-- Toasts will be added here dynamically -->
    </div>

    <!-- Scripts -->
    <script>
        // Global JavaScript utilities
        const HeartGrid = {
            // API base URL
            apiUrl: '/api/v1',
            
            // Show toast notification
            showToast: function(message, type = 'info') {
                const toastContainer = document.getElementById('toast-container');
                const toast = document.createElement('div');
                
                const typeClasses = {
                    'success': 'alert-success',
                    'error': 'alert-error',
                    'warning': 'alert-warning',
                    'info': 'alert-info'
                };
                
                toast.className = `alert ${typeClasses[type]} mb-2`;
                toast.innerHTML = `
                    <span>${message}</span>
                    <button class="btn btn-sm btn-ghost" onclick="this.parentElement.remove()">
                        <i class="fas fa-times"></i>
                    </button>
                `;
                
                toastContainer.appendChild(toast);
                
                // Auto remove after 5 seconds
                setTimeout(() => {
                    if (toast.parentElement) {
                        toast.remove();
                    }
                }, 5000);
            },
            
            // Make API request with authentication
            apiRequest: async function(endpoint, options = {}) {
                const token = localStorage.getItem('auth_token');
                const defaultOptions = {
                    headers: {
                        'Content-Type': 'application/json',
                        ...(token && { 'Authorization': `Token ${token}` })
                    }
                };
                
                const response = await fetch(this.apiUrl + endpoint, {
                    ...defaultOptions,
                    ...options,
                    headers: { ...defaultOptions.headers, ...options.headers }
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                return response.json();
            },
            
            // Load notifications
            loadNotifications: async function() {
                try {
                    const data = await this.apiRequest('/notifications/');
                    const unreadCount = data.notifications.filter(n => !n.is_read).length;
                    
                    // Update notification badge
                    const badge = document.getElementById('notification-count');
                    if (unreadCount > 0) {
                        badge.textContent = unreadCount;
                        badge.style.display = 'flex';
                    } else {
                        badge.style.display = 'none';
                    }
                    
                    // Update notifications list
                    const list = document.getElementById('notifications-list');
                    list.innerHTML = data.notifications.slice(0, 5).map(notification => `
                        <div class="p-2 rounded ${notification.is_read ? 'bg-base-200' : 'bg-primary/10'}">
                            <div class="font-semibold">${notification.title}</div>
                            <div class="text-sm opacity-70">${notification.message}</div>
                            <div class="text-xs opacity-50">${new Date(notification.created_at).toLocaleDateString()}</div>
                        </div>
                    `).join('');
                } catch (error) {
                    console.error('Failed to load notifications:', error);
                }
            }
        };
        
        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            // Load notifications if user is authenticated
            {% if user.is_authenticated %}
                HeartGrid.loadNotifications();
                
                // Refresh notifications every 30 seconds
                setInterval(() => {
                    HeartGrid.loadNotifications();
                }, 30000);
            {% endif %}
        });
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
