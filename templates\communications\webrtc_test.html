<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebRTC Calling Test - HeartGrid</title>
    
    <!-- DaisyUI and Tailwind CSS -->
    <link href="https://cdn.jsdelivr.net/npm/daisyui@4.4.24/dist/full.min.css" rel="stylesheet" type="text/css" />
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        .video-container {
            position: relative;
            background: #000;
            border-radius: 0.5rem;
            overflow: hidden;
        }
        
        .video-container video {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .pip-video {
            position: absolute;
            top: 1rem;
            right: 1rem;
            width: 150px;
            height: 100px;
            border: 2px solid white;
            border-radius: 0.5rem;
            overflow: hidden;
            z-index: 10;
        }
    </style>
</head>
<body class="bg-base-200 min-h-screen">
    <!-- User data for JavaScript -->
    <div id="user-data" 
         data-user-id="1" 
         data-user-name="Test User" 
         style="display: none;"></div>

    <div class="container mx-auto p-4">
        <div class="card bg-base-100 shadow-xl">
            <div class="card-body">
                <h1 class="card-title text-2xl mb-6">
                    <i class="fas fa-phone text-primary"></i>
                    WebRTC Calling Test
                </h1>
                
                <!-- Connection Status -->
                <div class="alert alert-info mb-4">
                    <i class="fas fa-info-circle"></i>
                    <div>
                        <h3 class="font-bold">Connection Status</h3>
                        <div class="text-sm">
                            <span id="connection-status">Connecting...</span>
                        </div>
                    </div>
                </div>
                
                <!-- Test Controls -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                    <div class="card bg-base-200">
                        <div class="card-body">
                            <h3 class="card-title text-lg">
                                <i class="fas fa-phone text-green-600"></i>
                                Voice Call Test
                            </h3>
                            <p class="text-sm opacity-70 mb-4">
                                Test voice calling functionality
                            </p>
                            <div class="form-control">
                                <label class="label">
                                    <span class="label-text">Call User ID:</span>
                                </label>
                                <input type="text" 
                                       id="voice-call-user-id" 
                                       placeholder="Enter user ID to call" 
                                       class="input input-bordered" 
                                       value="2">
                            </div>
                            <div class="card-actions justify-end mt-4">
                                <button id="start-voice-call" class="btn btn-success">
                                    <i class="fas fa-phone"></i>
                                    Start Voice Call
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card bg-base-200">
                        <div class="card-body">
                            <h3 class="card-title text-lg">
                                <i class="fas fa-video text-blue-600"></i>
                                Video Call Test
                            </h3>
                            <p class="text-sm opacity-70 mb-4">
                                Test video calling functionality
                            </p>
                            <div class="form-control">
                                <label class="label">
                                    <span class="label-text">Call User ID:</span>
                                </label>
                                <input type="text" 
                                       id="video-call-user-id" 
                                       placeholder="Enter user ID to call" 
                                       class="input input-bordered" 
                                       value="2">
                            </div>
                            <div class="card-actions justify-end mt-4">
                                <button id="start-video-call" class="btn btn-primary">
                                    <i class="fas fa-video"></i>
                                    Start Video Call
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Media Permissions Test -->
                <div class="card bg-base-200 mb-6">
                    <div class="card-body">
                        <h3 class="card-title text-lg">
                            <i class="fas fa-microphone text-orange-600"></i>
                            Media Permissions Test
                        </h3>
                        <p class="text-sm opacity-70 mb-4">
                            Test camera and microphone access
                        </p>
                        <div class="flex gap-2">
                            <button id="test-audio" class="btn btn-outline">
                                <i class="fas fa-microphone"></i>
                                Test Audio
                            </button>
                            <button id="test-video" class="btn btn-outline">
                                <i class="fas fa-camera"></i>
                                Test Video
                            </button>
                            <button id="stop-media-test" class="btn btn-outline btn-error">
                                <i class="fas fa-stop"></i>
                                Stop Test
                            </button>
                        </div>
                        
                        <!-- Local video preview -->
                        <div id="media-test-container" class="mt-4" style="display: none;">
                            <div class="video-container h-48">
                                <video id="test-video-element" autoplay muted playsinline></video>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- WebRTC Support Check -->
                <div class="card bg-base-200">
                    <div class="card-body">
                        <h3 class="card-title text-lg">
                            <i class="fas fa-check-circle text-success"></i>
                            Browser Support
                        </h3>
                        <div class="space-y-2">
                            <div class="flex items-center gap-2">
                                <span id="webrtc-support" class="badge badge-outline">Checking...</span>
                                <span>WebRTC Support</span>
                            </div>
                            <div class="flex items-center gap-2">
                                <span id="media-support" class="badge badge-outline">Checking...</span>
                                <span>Media Devices Support</span>
                            </div>
                            <div class="flex items-center gap-2">
                                <span id="websocket-support" class="badge badge-outline">Checking...</span>
                                <span>WebSocket Support</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Load JavaScript files -->
    <script src="/static/js/communications.js"></script>
    <script src="/static/js/webrtc-manager.js"></script>
    
    <script>
        // Test page specific JavaScript
        document.addEventListener('DOMContentLoaded', function() {
            // Check browser support
            checkBrowserSupport();
            
            // Setup test controls
            setupTestControls();
            
            // Monitor connection status
            monitorConnectionStatus();
        });
        
        function checkBrowserSupport() {
            // Check WebRTC support
            const webrtcSupported = !!(window.RTCPeerConnection && navigator.mediaDevices);
            document.getElementById('webrtc-support').textContent = webrtcSupported ? 'Supported' : 'Not Supported';
            document.getElementById('webrtc-support').className = webrtcSupported ? 'badge badge-success' : 'badge badge-error';
            
            // Check media devices support
            const mediaSupported = !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia);
            document.getElementById('media-support').textContent = mediaSupported ? 'Supported' : 'Not Supported';
            document.getElementById('media-support').className = mediaSupported ? 'badge badge-success' : 'badge badge-error';
            
            // Check WebSocket support
            const websocketSupported = !!window.WebSocket;
            document.getElementById('websocket-support').textContent = websocketSupported ? 'Supported' : 'Not Supported';
            document.getElementById('websocket-support').className = websocketSupported ? 'badge badge-success' : 'badge badge-error';
        }
        
        function setupTestControls() {
            // Voice call button
            document.getElementById('start-voice-call').addEventListener('click', function() {
                const userId = document.getElementById('voice-call-user-id').value;
                if (userId && window.heartGridComms) {
                    window.heartGridComms.initiateVoiceCall(userId);
                } else {
                    alert('Please enter a user ID and ensure communications are connected');
                }
            });
            
            // Video call button
            document.getElementById('start-video-call').addEventListener('click', function() {
                const userId = document.getElementById('video-call-user-id').value;
                if (userId && window.heartGridComms) {
                    window.heartGridComms.initiateVideoCall(userId);
                } else {
                    alert('Please enter a user ID and ensure communications are connected');
                }
            });
            
            // Media test buttons
            document.getElementById('test-audio').addEventListener('click', testAudio);
            document.getElementById('test-video').addEventListener('click', testVideo);
            document.getElementById('stop-media-test').addEventListener('click', stopMediaTest);
        }
        
        async function testAudio() {
            try {
                const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                console.log('Audio access granted');
                alert('Audio access granted successfully!');
                stream.getTracks().forEach(track => track.stop());
            } catch (error) {
                console.error('Audio access denied:', error);
                alert('Audio access denied: ' + error.message);
            }
        }
        
        async function testVideo() {
            try {
                const stream = await navigator.mediaDevices.getUserMedia({ video: true, audio: true });
                console.log('Video access granted');
                
                const videoElement = document.getElementById('test-video-element');
                videoElement.srcObject = stream;
                document.getElementById('media-test-container').style.display = 'block';
                
                // Store stream for cleanup
                window.testStream = stream;
            } catch (error) {
                console.error('Video access denied:', error);
                alert('Video access denied: ' + error.message);
            }
        }
        
        function stopMediaTest() {
            if (window.testStream) {
                window.testStream.getTracks().forEach(track => track.stop());
                window.testStream = null;
            }
            
            const videoElement = document.getElementById('test-video-element');
            videoElement.srcObject = null;
            document.getElementById('media-test-container').style.display = 'none';
        }
        
        function monitorConnectionStatus() {
            setInterval(function() {
                const statusElement = document.getElementById('connection-status');
                
                if (window.heartGridComms) {
                    const chatConnected = window.heartGridComms.chatSocket && 
                                        window.heartGridComms.chatSocket.readyState === WebSocket.OPEN;
                    const presenceConnected = window.heartGridComms.presenceSocket && 
                                            window.heartGridComms.presenceSocket.readyState === WebSocket.OPEN;
                    
                    if (chatConnected && presenceConnected) {
                        statusElement.textContent = 'Connected (Chat & Presence)';
                        statusElement.parentElement.parentElement.className = 'alert alert-success';
                    } else if (presenceConnected) {
                        statusElement.textContent = 'Partially Connected (Presence only)';
                        statusElement.parentElement.parentElement.className = 'alert alert-warning';
                    } else {
                        statusElement.textContent = 'Disconnected';
                        statusElement.parentElement.parentElement.className = 'alert alert-error';
                    }
                } else {
                    statusElement.textContent = 'Communications not initialized';
                    statusElement.parentElement.parentElement.className = 'alert alert-error';
                }
            }, 1000);
        }
    </script>
</body>
</html>
