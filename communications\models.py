"""
Communications Models for HeartGrid Dating Platform

This module contains models for real-time messaging, user presence tracking,
and communication-related functionality.
"""

import uuid
from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.core.exceptions import ValidationError

User = get_user_model()


class UserPresence(models.Model):
    """
    Tracks user online/offline status and last seen information
    """
    STATUS_CHOICES = [
        ('online', 'Online'),
        ('away', 'Away'),
        ('offline', 'Offline'),
    ]

    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='presence')
    status = models.CharField(max_length=10, choices=STATUS_CHOICES, default='offline')
    last_seen = models.DateTimeField(auto_now=True)
    last_activity = models.DateTimeField(auto_now=True)
    is_typing_to = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='typing_indicators')

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'communications_user_presence'

    def __str__(self):
        return f"{self.user.name} - {self.status}"

    def set_online(self):
        """Set user status to online"""
        self.status = 'online'
        self.last_activity = timezone.now()
        self.save()

    def set_offline(self):
        """Set user status to offline"""
        self.status = 'offline'
        self.last_seen = timezone.now()
        self.save()

    def set_typing(self, to_user):
        """Set typing indicator for a specific user"""
        self.is_typing_to = to_user
        self.save()

    def clear_typing(self):
        """Clear typing indicator"""
        self.is_typing_to = None
        self.save()


class Conversation(models.Model):
    """
    Represents a conversation between two users
    """
    CONVERSATION_TYPES = [
        ('direct', 'Direct Message'),
        ('match', 'Match Conversation'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    participants = models.ManyToManyField(User, related_name='conversations')
    conversation_type = models.CharField(max_length=10, choices=CONVERSATION_TYPES, default='direct')

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    last_message_at = models.DateTimeField(null=True, blank=True)

    # Conversation settings
    is_active = models.BooleanField(default=True)
    is_archived = models.BooleanField(default=False)

    class Meta:
        db_table = 'communications_conversations'
        ordering = ['-last_message_at', '-updated_at']

    def __str__(self):
        participant_names = ", ".join([user.name for user in self.participants.all()[:2]])
        return f"Conversation: {participant_names}"

    def get_other_participant(self, user):
        """Get the other participant in a direct conversation"""
        return self.participants.exclude(id=user.id).first()

    def update_last_message_time(self):
        """Update the last message timestamp"""
        self.last_message_at = timezone.now()
        self.save(update_fields=['last_message_at'])


class Message(models.Model):
    """
    Individual messages within conversations
    """
    MESSAGE_TYPES = [
        ('text', 'Text Message'),
        ('image', 'Image'),
        ('file', 'File'),
        ('system', 'System Message'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    conversation = models.ForeignKey(Conversation, on_delete=models.CASCADE, related_name='messages')
    sender = models.ForeignKey(User, on_delete=models.CASCADE, related_name='communications_sent_messages')

    # Message content
    message_type = models.CharField(max_length=10, choices=MESSAGE_TYPES, default='text')
    content = models.TextField(blank=True)
    file_attachment = models.FileField(upload_to='messages/attachments/', null=True, blank=True)

    # Message metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    edited_at = models.DateTimeField(null=True, blank=True)

    # Message status
    is_deleted = models.BooleanField(default=False)
    deleted_at = models.DateTimeField(null=True, blank=True)

    # Reactions (emoji reactions to messages)
    reactions = models.JSONField(default=dict, blank=True)  # {user_id: emoji}

    class Meta:
        db_table = 'communications_messages'
        ordering = ['created_at']

    def __str__(self):
        return f"Message from {self.sender.name} in {self.conversation}"

    def save(self, *args, **kwargs):
        is_new = self.pk is None
        super().save(*args, **kwargs)

        # Update conversation's last message time
        if is_new:
            self.conversation.update_last_message_time()

    def soft_delete(self):
        """Soft delete the message"""
        self.is_deleted = True
        self.deleted_at = timezone.now()
        self.save()

    def add_reaction(self, user, emoji):
        """Add or update a reaction to the message"""
        self.reactions[str(user.id)] = emoji
        self.save()

    def remove_reaction(self, user):
        """Remove a user's reaction from the message"""
        self.reactions.pop(str(user.id), None)
        self.save()


class MessageStatus(models.Model):
    """
    Tracks read/unread status of messages for each user
    """
    STATUS_CHOICES = [
        ('sent', 'Sent'),
        ('delivered', 'Delivered'),
        ('read', 'Read'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    message = models.ForeignKey(Message, on_delete=models.CASCADE, related_name='statuses')
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='message_statuses')
    status = models.CharField(max_length=10, choices=STATUS_CHOICES, default='sent')

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    read_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        db_table = 'communications_message_status'
        unique_together = ['message', 'user']

    def __str__(self):
        return f"{self.user.name} - {self.message} - {self.status}"

    def mark_as_read(self):
        """Mark message as read"""
        self.status = 'read'
        self.read_at = timezone.now()
        self.save()

    def mark_as_delivered(self):
        """Mark message as delivered"""
        if self.status == 'sent':
            self.status = 'delivered'
            self.save()


class ConversationParticipant(models.Model):
    """
    Tracks participant-specific settings for conversations
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    conversation = models.ForeignKey(Conversation, on_delete=models.CASCADE, related_name='participant_settings')
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='conversation_settings')

    # Participant settings
    is_muted = models.BooleanField(default=False)
    is_archived = models.BooleanField(default=False)
    last_read_message = models.ForeignKey(Message, on_delete=models.SET_NULL, null=True, blank=True)
    last_read_at = models.DateTimeField(null=True, blank=True)

    # Notification settings
    notifications_enabled = models.BooleanField(default=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'communications_conversation_participants'
        unique_together = ['conversation', 'user']

    def __str__(self):
        return f"{self.user.name} in {self.conversation}"

    def mark_as_read(self, message=None):
        """Mark conversation as read up to a specific message"""
        if message is None:
            # Mark as read up to the latest message
            message = self.conversation.messages.filter(is_deleted=False).last()

        if message:
            self.last_read_message = message
            self.last_read_at = timezone.now()
            self.save()

    def get_unread_count(self):
        """Get count of unread messages"""
        if not self.last_read_message:
            return self.conversation.messages.filter(is_deleted=False).exclude(sender=self.user).count()

        return self.conversation.messages.filter(
            is_deleted=False,
            created_at__gt=self.last_read_message.created_at
        ).exclude(sender=self.user).count()
