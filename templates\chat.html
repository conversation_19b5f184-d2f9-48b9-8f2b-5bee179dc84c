{% extends "base.html" %}

{% block title %}Chat - HeartGrid{% endblock %}

{% block content %}
<div class="container-fluid py-3">
    <div class="row justify-content-center">
        <div class="col-lg-8 col-xl-6">
            <div class="chat-container">
                <!-- Chat <PERSON>er -->
                <div class="chat-header">
                    <div class="d-flex align-items-center">
                        <a href="{% url 'heartgrid_frontend:matches_page' %}" class="btn btn-outline-secondary btn-sm me-3">
                            <i class="fas fa-arrow-left"></i>
                        </a>
                        
                        <div class="chat-avatar me-3">
                            {% if match_profile.photos %}
                                <img src="/media/{{ match_profile.photos.0 }}"
                                     alt="Profile Photo" class="rounded-circle">
                            {% else %}
                                <div class="avatar-placeholder">
                                    <i class="fas fa-user"></i>
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="chat-info">
                            <h5 class="mb-0">{{ match_user.name if match_user else 'User' }}</h5>
                            {% if match_profile.location %}
                                <small class="text-muted">
                                    <i class="fas fa-map-marker-alt me-1"></i>
                                    {{ match_profile.location }}
                                </small>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <!-- Chat Messages -->
                <div class="chat-messages" id="chatMessages">
                    {% if conversation %}
                        {% for message in conversation %}
                            <div class="message {{ 'own-message' if message.sender_id == session.user_id else 'other-message' }}">
                                <div class="message-content">
                                    {{ message.message }}
                                </div>
                                <div class="message-time">
                                    {{ message.timestamp.strftime('%I:%M %p') }}
                                </div>
                            </div>
                        {% endfor %}
                    {% else %}
                        <div class="chat-starter text-center py-4">
                            <i class="fas fa-heart fa-2x text-danger mb-3"></i>
                            <p class="text-muted">You matched! Start the conversation.</p>
                        </div>
                    {% endif %}
                </div>
                
                <!-- Chat Input -->
                <div class="chat-input">
                    <form id="messageForm" class="d-flex">
                        <input type="text" class="form-control me-2" id="messageInput" 
                               placeholder="Type a message..." maxlength="1000" required>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
const matchUserId = '{{ match_user_id }}';
const currentUserId = '{{ session.user_id }}';

// Auto-scroll to bottom
function scrollToBottom() {
    const chatMessages = document.getElementById('chatMessages');
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    scrollToBottom();
    
    // Focus on input
    document.getElementById('messageInput').focus();
});

// Handle message form submission
document.getElementById('messageForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const messageInput = document.getElementById('messageInput');
    const message = messageInput.value.trim();
    
    if (!message) return;
    
    // Disable input while sending
    messageInput.disabled = true;
    
    // Send message
    fetch('/send_message', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            receiver_id: matchUserId,
            message: message
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Add message to chat immediately
            addMessageToChat(message, true, new Date());
            messageInput.value = '';
            scrollToBottom();
        } else {
            alert('Failed to send message: ' + (data.error || 'Unknown error'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Failed to send message');
    })
    .finally(() => {
        messageInput.disabled = false;
        messageInput.focus();
    });
});

// Add message to chat UI
function addMessageToChat(message, isOwn, timestamp) {
    const chatMessages = document.getElementById('chatMessages');
    
    // Remove starter message if it exists
    const starter = chatMessages.querySelector('.chat-starter');
    if (starter) {
        starter.remove();
    }
    
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${isOwn ? 'own-message' : 'other-message'}`;
    
    const time = new Date(timestamp);
    const timeString = time.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
    
    messageDiv.innerHTML = `
        <div class="message-content">${escapeHtml(message)}</div>
        <div class="message-time">${timeString}</div>
    `;
    
    chatMessages.appendChild(messageDiv);
}

// Utility function to escape HTML
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// Periodically check for new messages
function checkForNewMessages() {
    fetch(`/get_messages/${matchUserId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Get current message count
                const currentMessages = document.querySelectorAll('.message').length;
                
                if (data.messages.length > currentMessages) {
                    // Clear and rebuild messages
                    const chatMessages = document.getElementById('chatMessages');
                    chatMessages.innerHTML = '';
                    
                    if (data.messages.length === 0) {
                        chatMessages.innerHTML = `
                            <div class="chat-starter text-center py-4">
                                <i class="fas fa-heart fa-2x text-danger mb-3"></i>
                                <p class="text-muted">You matched! Start the conversation.</p>
                            </div>
                        `;
                    } else {
                        data.messages.forEach(msg => {
                            addMessageToChat(msg.message, msg.is_own, msg.timestamp);
                        });
                        scrollToBottom();
                    }
                }
            }
        })
        .catch(error => {
            console.error('Error checking messages:', error);
        });
}

// Check for new messages every 3 seconds
setInterval(checkForNewMessages, 3000);
</script>
{% endblock %}
