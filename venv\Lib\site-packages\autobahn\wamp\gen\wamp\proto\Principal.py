# automatically generated by the FlatBuffers compiler, do not modify

# namespace: proto

import flatbuffers
from flatbuffers.compat import import_numpy
np = import_numpy()

class Principal(object):
    __slots__ = ['_tab']

    @classmethod
    def SizeOf(cls):
        return 8

    # Principal
    def Init(self, buf, pos):
        self._tab = flatbuffers.table.Table(buf, pos)

    # Principal
    def Session(self): return self._tab.Get(flatbuffers.number_types.Uint64Flags, self._tab.Pos + flatbuffers.number_types.UOffsetTFlags.py_type(0))

def CreatePrincipal(builder, session):
    builder.Prep(8, 8)
    builder.PrependUint64(session)
    return builder.Offset()
