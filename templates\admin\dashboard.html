{% extends "base.html" %}

{% block title %}Admin Dashboard - HeartGrid{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Admin Dashboard</h1>
            <p class="text-muted">Monitor HeartGrid platform analytics and user activity</p>
        </div>
        <div>
            <a href="{{ url_for('admin_users') }}" class="btn btn-primary">
                <i class="fas fa-users me-2"></i>Manage Users
            </a>
        </div>
    </div>

    <!-- Stats Cards Row -->
    <div class="row g-4 mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Users
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ stats.total_users }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Active Users
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ stats.active_users }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-user-check fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Total Matches
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ stats.total_matches }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-heart fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Active Subscriptions
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ stats.active_subscriptions }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-crown fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Revenue and Charts Row -->
    <div class="row g-4 mb-4">
        <div class="col-xl-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Platform Metrics</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-4">
                            <div class="metric-card">
                                <h3 class="text-primary">{{ stats.total_users }}</h3>
                                <p class="text-muted">Total Users</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="metric-card">
                                <h3 class="text-success">{{ stats.total_matches }}</h3>
                                <p class="text-muted">Total Matches</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="metric-card">
                                <h3 class="text-warning">${{ stats.subscription_revenue }}</h3>
                                <p class="text-muted">Monthly Revenue</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <a href="{{ url_for('admin_users') }}" class="list-group-item list-group-item-action">
                            <i class="fas fa-users me-2"></i>Manage Users
                        </a>
                        <a href="#" class="list-group-item list-group-item-action" onclick="exportData()">
                            <i class="fas fa-download me-2"></i>Export Data
                        </a>
                        <a href="#" class="list-group-item list-group-item-action" onclick="viewReports()">
                            <i class="fas fa-chart-bar me-2"></i>View Reports
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="row">
        <div class="col-lg-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Platform Activity</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Metric</th>
                                    <th>Current Value</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Total Users</td>
                                    <td>{{ stats.total_users }}</td>
                                    <td><span class="badge bg-success">Active</span></td>
                                </tr>
                                <tr>
                                    <td>Active Users</td>
                                    <td>{{ stats.active_users }}</td>
                                    <td><span class="badge bg-success">Engaged</span></td>
                                </tr>
                                <tr>
                                    <td>Total Matches</td>
                                    <td>{{ stats.total_matches }}</td>
                                    <td><span class="badge bg-info">Growing</span></td>
                                </tr>
                                <tr>
                                    <td>Active Subscriptions</td>
                                    <td>{{ stats.active_subscriptions }}</td>
                                    <td><span class="badge bg-warning">Premium</span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}

.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}

.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}

.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}

.text-xs {
    font-size: .7rem;
}

.font-weight-bold {
    font-weight: 700 !important;
}

.text-gray-800 {
    color: #5a5c69 !important;
}

.text-gray-300 {
    color: #dddfeb !important;
}

.metric-card {
    padding: 20px;
    border-radius: 10px;
    margin: 10px 0;
}
</style>

<script>
function exportData() {
    showNotification('Data export feature coming soon!', 'info');
}

function viewReports() {
    showNotification('Advanced reports feature coming soon!', 'info');
}
</script>
{% endblock %}