{% extends "base_modern.html" %}

{% block title %}Discover - HeartGrid{% endblock %}

{% block extra_css %}
<style>
    .swipe-container {
        position: relative;
        width: 100%;
        max-width: 400px;
        height: 600px;
        margin: 0 auto;
        perspective: 1000px;
    }
    
    .profile-card {
        position: absolute;
        width: 100%;
        height: 100%;
        border-radius: 20px;
        overflow: hidden;
        cursor: grab;
        transform-style: preserve-3d;
        transition: transform 0.3s ease;
        box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    }
    
    .profile-card:active {
        cursor: grabbing;
    }
    
    .profile-image {
        width: 100%;
        height: 70%;
        object-fit: cover;
        background: linear-gradient(45deg, #f0f0f0, #e0e0e0);
    }
    
    .profile-info {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background: linear-gradient(transparent, rgba(0,0,0,0.8));
        color: white;
        padding: 2rem 1.5rem 1.5rem;
    }
    
    .action-buttons {
        position: absolute;
        bottom: -80px;
        left: 50%;
        transform: translateX(-50%);
        display: flex;
        gap: 1rem;
        z-index: 10;
    }
    
    .action-btn {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        border: none;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    }
    
    .action-btn:hover {
        transform: scale(1.1);
    }
    
    .btn-pass {
        background: #ff4757;
        color: white;
    }
    
    .btn-super-like {
        background: #3742fa;
        color: white;
    }
    
    .btn-like {
        background: #2ed573;
        color: white;
    }
    
    .match-modal {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000;
    }
    
    .filters-sidebar {
        position: fixed;
        top: 0;
        right: -300px;
        width: 300px;
        height: 100vh;
        background: white;
        box-shadow: -5px 0 15px rgba(0,0,0,0.1);
        transition: right 0.3s ease;
        z-index: 100;
        overflow-y: auto;
    }
    
    .filters-sidebar.open {
        right: 0;
    }
</style>
{% endblock %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-pink-50 to-purple-50">
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-3xl font-bold text-gray-800">Discover</h1>
            <p class="text-gray-600">Find your perfect match</p>
        </div>
        
        <div class="flex gap-2">
            <!-- Filters Button -->
            <button class="btn btn-outline btn-primary" onclick="toggleFilters()">
                <i class="fas fa-filter mr-2"></i>Filters
            </button>
            
            <!-- Premium Features -->
            {% if user.subscription.is_active %}
                <div class="dropdown dropdown-end">
                    <div tabindex="0" role="button" class="btn btn-primary">
                        <i class="fas fa-crown mr-2"></i>Premium
                    </div>
                    <ul tabindex="0" class="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-52">
                        <li><a onclick="enableBoost()"><i class="fas fa-rocket mr-2"></i>Boost Profile</a></li>
                        <li><a onclick="showWhoLikesYou()"><i class="fas fa-heart mr-2"></i>Who Likes You</a></li>
                        <li><a onclick="enableRewind()"><i class="fas fa-undo mr-2"></i>Rewind Last Swipe</a></li>
                    </ul>
                </div>
            {% endif %}
        </div>
    </div>

    <!-- Main Swipe Area -->
    <div class="flex justify-center">
        <div class="swipe-container" id="swipe-container">
            <!-- Profile cards will be loaded here -->
            <div class="flex items-center justify-center h-full" id="loading-state">
                <div class="text-center">
                    <span class="loading loading-spinner loading-lg text-primary"></span>
                    <p class="mt-4 text-gray-600">Finding amazing people for you...</p>
                </div>
            </div>
            
            <!-- No more profiles state -->
            <div class="hidden items-center justify-center h-full text-center" id="no-profiles-state">
                <div>
                    <i class="fas fa-heart text-6xl text-gray-300 mb-4"></i>
                    <h3 class="text-xl font-semibold text-gray-700 mb-2">No more profiles</h3>
                    <p class="text-gray-500 mb-4">Check back later for new people!</p>
                    <button class="btn btn-primary" onclick="loadProfiles()">
                        <i class="fas fa-refresh mr-2"></i>Refresh
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="action-buttons">
        <button class="action-btn btn-pass" onclick="passProfile()" title="Pass">
            <i class="fas fa-times"></i>
        </button>
        
        {% if user.subscription.has_feature.super_likes %}
        <button class="action-btn btn-super-like" onclick="superLikeProfile()" title="Super Like">
            <i class="fas fa-star"></i>
        </button>
        {% endif %}
        
        <button class="action-btn btn-like" onclick="likeProfile()" title="Like">
            <i class="fas fa-heart"></i>
        </button>
    </div>

    <!-- Stats Card -->
    <div class="card bg-base-100 shadow-xl mt-8 max-w-md mx-auto">
        <div class="card-body">
            <h3 class="card-title text-center">Today's Activity</h3>
            <div class="stats stats-vertical lg:stats-horizontal">
                <div class="stat">
                    <div class="stat-title">Likes Sent</div>
                    <div class="stat-value text-primary" id="likes-sent">0</div>
                </div>
                <div class="stat">
                    <div class="stat-title">Matches</div>
                    <div class="stat-value text-secondary" id="matches-count">0</div>
                </div>
                <div class="stat">
                    <div class="stat-title">Super Likes</div>
                    <div class="stat-value text-accent" id="super-likes">0</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filters Sidebar -->
<div class="filters-sidebar" id="filters-sidebar">
    <div class="p-6">
        <div class="flex justify-between items-center mb-6">
            <h2 class="text-xl font-bold">Filters</h2>
            <button class="btn btn-ghost btn-sm" onclick="toggleFilters()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <div class="space-y-6">
            <!-- Age Range -->
            <div>
                <label class="label">
                    <span class="label-text font-semibold">Age Range</span>
                </label>
                <div class="flex items-center gap-4">
                    <input type="range" min="18" max="65" value="25" class="range range-primary" id="min-age">
                    <span class="text-sm">to</span>
                    <input type="range" min="18" max="65" value="35" class="range range-primary" id="max-age">
                </div>
                <div class="flex justify-between text-sm text-gray-500">
                    <span id="min-age-display">25</span>
                    <span id="max-age-display">35</span>
                </div>
            </div>
            
            <!-- Distance -->
            <div>
                <label class="label">
                    <span class="label-text font-semibold">Distance</span>
                </label>
                <input type="range" min="1" max="100" value="25" class="range range-secondary" id="distance">
                <div class="text-center text-sm text-gray-500">
                    <span id="distance-display">25</span> km
                </div>
            </div>
            
            <!-- Interests -->
            <div>
                <label class="label">
                    <span class="label-text font-semibold">Interests</span>
                </label>
                <div class="flex flex-wrap gap-2">
                    <input type="checkbox" class="btn btn-outline btn-sm" id="music" aria-label="Music">
                    <label for="music" class="btn btn-outline btn-sm">Music</label>
                    
                    <input type="checkbox" class="btn btn-outline btn-sm" id="travel" aria-label="Travel">
                    <label for="travel" class="btn btn-outline btn-sm">Travel</label>
                    
                    <input type="checkbox" class="btn btn-outline btn-sm" id="fitness" aria-label="Fitness">
                    <label for="fitness" class="btn btn-outline btn-sm">Fitness</label>
                    
                    <input type="checkbox" class="btn btn-outline btn-sm" id="food" aria-label="Food">
                    <label for="food" class="btn btn-outline btn-sm">Food</label>
                </div>
            </div>
            
            <!-- Apply Filters -->
            <button class="btn btn-primary btn-block" onclick="applyFilters()">
                Apply Filters
            </button>
        </div>
    </div>
</div>

<!-- Match Modal -->
<div class="modal" id="match-modal">
    <div class="modal-box text-center">
        <div class="match-animation text-6xl mb-4">💕</div>
        <h3 class="font-bold text-lg mb-2">It's a Match!</h3>
        <p class="py-4">You and <span id="match-name"></span> liked each other</p>
        <div class="modal-action justify-center">
            <button class="btn btn-primary" onclick="sendMessage()">Send Message</button>
            <button class="btn btn-ghost" onclick="closeMatchModal()">Keep Swiping</button>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentProfiles = [];
let currentIndex = 0;
let stats = { likes: 0, matches: 0, superLikes: 0 };

// Load profiles from API
async function loadProfiles() {
    try {
        document.getElementById('loading-state').classList.remove('hidden');
        document.getElementById('no-profiles-state').classList.add('hidden');
        
        const response = await HeartGrid.apiRequest('/discover/');
        currentProfiles = response.profiles;
        currentIndex = 0;
        
        if (currentProfiles.length === 0) {
            showNoProfilesState();
        } else {
            renderCurrentProfile();
        }
        
        document.getElementById('loading-state').classList.add('hidden');
    } catch (error) {
        console.error('Failed to load profiles:', error);
        HeartGrid.showToast('Failed to load profiles', 'error');
    }
}

// Render current profile
function renderCurrentProfile() {
    if (currentIndex >= currentProfiles.length) {
        showNoProfilesState();
        return;
    }
    
    const profile = currentProfiles[currentIndex];
    const container = document.getElementById('swipe-container');
    
    container.innerHTML = `
        <div class="profile-card swipe-card" data-profile-id="${profile.id}">
            <img src="${profile.photos[0]?.image || '/static/images/default-avatar.png'}" 
                 alt="${profile.name}" class="profile-image">
            <div class="profile-info">
                <h2 class="text-2xl font-bold">${profile.name}, ${profile.age}</h2>
                <p class="text-sm opacity-90 mb-2">${profile.distance}km away</p>
                <p class="text-sm">${profile.bio || 'No bio available'}</p>
                ${profile.interests ? `
                    <div class="flex flex-wrap gap-1 mt-2">
                        ${profile.interests.map(interest => 
                            `<span class="badge badge-primary badge-sm">${interest}</span>`
                        ).join('')}
                    </div>
                ` : ''}
            </div>
        </div>
    `;
}

// Handle profile actions
async function likeProfile() {
    if (currentIndex >= currentProfiles.length) return;
    
    const profile = currentProfiles[currentIndex];
    
    try {
        const response = await HeartGrid.apiRequest('/like/', {
            method: 'POST',
            body: JSON.stringify({ user_id: profile.id })
        });
        
        stats.likes++;
        updateStats();
        
        if (response.is_match) {
            showMatchModal(profile.name, response.match_id);
            stats.matches++;
            updateStats();
        }
        
        nextProfile();
    } catch (error) {
        console.error('Failed to like profile:', error);
        HeartGrid.showToast('Failed to like profile', 'error');
    }
}

async function superLikeProfile() {
    if (currentIndex >= currentProfiles.length) return;
    
    const profile = currentProfiles[currentIndex];
    
    try {
        const response = await HeartGrid.apiRequest('/premium/super-like/', {
            method: 'POST',
            body: JSON.stringify({ user_id: profile.id })
        });
        
        stats.superLikes++;
        updateStats();
        
        if (response.is_match) {
            showMatchModal(profile.name, response.match_id);
            stats.matches++;
            updateStats();
        }
        
        HeartGrid.showToast('Super like sent!', 'success');
        nextProfile();
    } catch (error) {
        console.error('Failed to super like profile:', error);
        HeartGrid.showToast('Failed to send super like', 'error');
    }
}

function passProfile() {
    nextProfile();
}

function nextProfile() {
    currentIndex++;
    renderCurrentProfile();
}

function showNoProfilesState() {
    document.getElementById('loading-state').classList.add('hidden');
    document.getElementById('no-profiles-state').classList.remove('hidden');
}

function showMatchModal(name, matchId) {
    document.getElementById('match-name').textContent = name;
    document.getElementById('match-modal').classList.add('modal-open');
}

function closeMatchModal() {
    document.getElementById('match-modal').classList.remove('modal-open');
}

function sendMessage() {
    closeMatchModal();
    // Redirect to messages
    window.location.href = '/messages/';
}

function updateStats() {
    document.getElementById('likes-sent').textContent = stats.likes;
    document.getElementById('matches-count').textContent = stats.matches;
    document.getElementById('super-likes').textContent = stats.superLikes;
}

function toggleFilters() {
    const sidebar = document.getElementById('filters-sidebar');
    sidebar.classList.toggle('open');
}

function applyFilters() {
    // Collect filter values and reload profiles
    const filters = {
        min_age: document.getElementById('min-age').value,
        max_age: document.getElementById('max-age').value,
        distance: document.getElementById('distance').value
    };
    
    // Apply filters and reload
    loadProfiles();
    toggleFilters();
    HeartGrid.showToast('Filters applied', 'success');
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    loadProfiles();
    
    // Update filter displays
    document.getElementById('min-age').addEventListener('input', function() {
        document.getElementById('min-age-display').textContent = this.value;
    });
    
    document.getElementById('max-age').addEventListener('input', function() {
        document.getElementById('max-age-display').textContent = this.value;
    });
    
    document.getElementById('distance').addEventListener('input', function() {
        document.getElementById('distance-display').textContent = this.value;
    });
});
</script>
{% endblock %}
