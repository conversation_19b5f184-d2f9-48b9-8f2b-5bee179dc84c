{% extends "base.html" %}

{% block title %}Discover - HeartGrid{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="discover-header text-center mb-5">
        <i class="fas fa-compass fa-3x text-primary mb-3"></i>
        <h2 class="fw-bold">Discover Amazing People</h2>
        <p class="text-muted">Browse profiles in our unique grid layout</p>
    </div>
    
    <div class="discovery-controls mb-4">
        <div class="text-center">
            <h2 class="fw-bold text-primary">Discover People</h2>
            <p class="text-muted">Find your perfect match</p>
            <div class="mt-3">
                <a href="/api/v1/premium/filters/" class="btn btn-outline-primary">
                    <i class="fas fa-filter me-2"></i>Advanced Filters
                </a>
            </div>
        </div>
    </div>

    {% if profiles %}
        <div class="profiles-grid" id="profilesGrid">
            {% for profile in profiles %}
                <div class="profile-card" data-user-id="{{ profile.user_id }}" onclick="openProfileModal('{{ profile.user_id }}')">
                    <div class="profile-image">
                        {% if profile.photos %}
                            <img src="/media/{{ profile.photos.0 }}"
                                 alt="Profile Photo" class="img-fluid">
                        {% else %}
                            <div class="no-photo">
                                <i class="fas fa-user fa-3x text-muted"></i>
                            </div>
                        {% endif %}
                        
                        <!-- Profile Actions -->
                        <div class="profile-actions" onclick="event.stopPropagation()">
                            <button class="btn btn-warning btn-sm super-like-btn" title="Super Like" onclick="superLikeProfile('{{ profile.user_id }}', this)">
                                <i class="fas fa-star"></i>
                            </button>
                            <button class="btn btn-light btn-sm pass-btn" title="Pass" onclick="passProfile('{{ profile.user_id }}', this)">
                                <i class="fas fa-times"></i>
                            </button>
                            <button class="btn btn-danger btn-sm like-btn" title="Like" onclick="likeProfile('{{ profile.user_id }}', this)">
                                <i class="fas fa-heart"></i>
                            </button>
                        </div>
                        
                        <!-- Compatibility Score -->
                        {% if profile.compatibility_score is defined %}
                        <div class="compatibility-badge">
                            {{ (profile.compatibility_score * 100)|round }}% Match
                        </div>
                        {% endif %}
                    </div>
                    
                    <div class="profile-info">
                        <h5 class="profile-name mb-1">
                            {{ data_store.get_user_by_id(profile.user_id).name if data_store.get_user_by_id(profile.user_id) else 'User' }}
                            {% if profile.age %}
                                <span class="text-muted">, {{ profile.age }}</span>
                            {% endif %}
                        </h5>
                        
                        {% if profile.location %}
                            <p class="profile-location text-muted mb-2">
                                <i class="fas fa-map-marker-alt me-1"></i>
                                {{ profile.location }}
                            </p>
                        {% endif %}
                        
                        {% if profile.bio %}
                            <p class="profile-bio text-muted small">
                                {{ profile.bio[:100] }}{{ '...' if profile.bio|length > 100 else '' }}
                            </p>
                        {% endif %}
                        
                        {% if profile.interests %}
                            <div class="profile-interests">
                                {% for interest in profile.interests[:3] %}
                                    <span class="badge bg-light text-dark me-1">{{ interest }}</span>
                                {% endfor %}
                                {% if profile.interests|length > 3 %}
                                    <span class="badge bg-light text-dark">+{{ profile.interests|length - 3 }}</span>
                                {% endif %}
                            </div>
                        {% endif %}
                    </div>
                </div>
            {% endfor %}
        </div>
    {% else %}
        <div class="empty-state text-center py-5">
            <i class="fas fa-search fa-4x text-muted mb-4"></i>
            <h4 class="text-muted mb-3">No More Profiles</h4>
            <p class="text-muted mb-4">
                You've seen all available profiles! Check back later for new people to discover.
            </p>
            <a href="{% url 'heartgrid_frontend:matches_page' %}" class="btn btn-primary">
                <i class="fas fa-heart me-2"></i>
                View My Matches
            </a>
        </div>
    {% endif %}
</div>

<!-- Profile Detail Modal -->
<div class="modal fade" id="profileModal" tabindex="-1">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header border-0">
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body p-0" id="profileModalBody">
                <!-- Profile content will be loaded here -->
            </div>
        </div>
    </div>
</div>

<!-- Match Modal -->
<div class="modal fade" id="matchModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content match-modal">
            <div class="modal-body text-center p-5">
                <div class="match-animation mb-4">
                    <i class="fas fa-heart fa-4x text-danger heartbeat"></i>
                </div>
                <h3 class="fw-bold mb-3">It's a Match! 💕</h3>
                <p class="text-muted mb-4">You and <span id="matchName" class="fw-bold"></span> liked each other!</p>
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-primary btn-lg" onclick="startChat()">
                        <i class="fas fa-comments me-2"></i>
                        Start Chatting
                    </button>
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                        Keep Browsing
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let currentMatchId = null;

// Handle like button clicks
document.addEventListener('click', function(e) {
    if (e.target.closest('.like-btn')) {
        const profileCard = e.target.closest('.profile-card');
        const userId = profileCard.dataset.userId;
        likeProfile(userId, profileCard);
    } else if (e.target.closest('.pass-btn')) {
        const profileCard = e.target.closest('.profile-card');
        passProfile(profileCard);
    }
});

function likeProfile(userId, profileCard) {
    // Add visual feedback
    profileCard.classList.add('liking');
    
    fetch('/like_profile', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ user_id: userId })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Remove card with animation
            profileCard.classList.add('liked');
            setTimeout(() => {
                profileCard.remove();
            }, 500);
            
            // Check if it's a match
            if (data.is_match) {
                currentMatchId = data.match_id;
                document.getElementById('matchName').textContent = data.match_name || 'Someone special';
                
                // Show match modal after card animation
                setTimeout(() => {
                    const matchModal = new bootstrap.Modal(document.getElementById('matchModal'));
                    matchModal.show();
                }, 600);
            }
        } else {
            profileCard.classList.remove('liking');
            alert('Failed to like profile: ' + (data.error || 'Unknown error'));
        }
    })
    .catch(error => {
        profileCard.classList.remove('liking');
        console.error('Error:', error);
        alert('Failed to like profile');
    });
}

function passProfile(profileCard) {
    profileCard.classList.add('passed');
    setTimeout(() => {
        profileCard.remove();
    }, 500);
}

function startChat() {
    if (currentMatchId) {
        window.location.href = '/chat/' + currentMatchId.split('_')[1]; // Get the other user's ID
    }
}
</script>
{% endblock %}
