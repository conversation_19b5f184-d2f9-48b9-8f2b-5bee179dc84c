"""
Notification system for HeartGrid Communications

This module handles real-time notifications for messages and user status changes.
"""

import json
from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync
from django.contrib.auth import get_user_model
from .models import UserPresence, Conversation, Message

User = get_user_model()


class NotificationManager:
    """
    Manager class for handling real-time notifications
    """
    
    def __init__(self):
        self.channel_layer = get_channel_layer()
    
    def send_message_notification(self, message, exclude_user=None):
        """
        Send notification for a new message to all conversation participants
        
        Args:
            message: Message instance
            exclude_user: User to exclude from notifications (usually the sender)
        """
        conversation = message.conversation
        participants = conversation.participants.all()
        
        if exclude_user:
            participants = participants.exclude(id=exclude_user.id)
        
        notification_data = {
            'type': 'message_notification',
            'message_id': str(message.id),
            'conversation_id': str(conversation.id),
            'sender_id': str(message.sender.id),
            'sender_name': message.sender.name,
            'content': message.content,
            'message_type': message.message_type,
            'created_at': message.created_at.isoformat(),
            'conversation_type': conversation.conversation_type
        }
        
        for participant in participants:
            self._send_to_user(participant.id, notification_data)
    
    def send_presence_notification(self, user, status, last_seen=None):
        """
        Send presence update notification to relevant users
        
        Args:
            user: User whose presence changed
            status: New presence status
            last_seen: Last seen timestamp (for offline status)
        """
        # Get users who should receive this notification
        relevant_users = self._get_relevant_users_for_presence(user)
        
        notification_data = {
            'type': 'presence_notification',
            'user_id': str(user.id),
            'user_name': user.name,
            'status': status,
            'last_seen': last_seen.isoformat() if last_seen else None
        }
        
        for user_id in relevant_users:
            self._send_to_user(user_id, notification_data)
    
    def send_typing_notification(self, conversation, user, is_typing):
        """
        Send typing indicator notification to conversation participants
        
        Args:
            conversation: Conversation instance
            user: User who is typing
            is_typing: Boolean indicating if user is typing
        """
        participants = conversation.participants.exclude(id=user.id)
        
        notification_data = {
            'type': 'typing_notification',
            'conversation_id': str(conversation.id),
            'user_id': str(user.id),
            'user_name': user.name,
            'is_typing': is_typing
        }
        
        for participant in participants:
            self._send_to_user(participant.id, notification_data)
    
    def send_message_reaction_notification(self, message, user, emoji, action):
        """
        Send message reaction notification to conversation participants
        
        Args:
            message: Message instance
            user: User who reacted
            emoji: Reaction emoji
            action: 'add' or 'remove'
        """
        conversation = message.conversation
        participants = conversation.participants.exclude(id=user.id)
        
        notification_data = {
            'type': 'reaction_notification',
            'message_id': str(message.id),
            'conversation_id': str(conversation.id),
            'user_id': str(user.id),
            'user_name': user.name,
            'emoji': emoji,
            'action': action,
            'reactions': message.reactions
        }
        
        for participant in participants:
            self._send_to_user(participant.id, notification_data)
    
    def send_conversation_notification(self, conversation, notification_type, data=None):
        """
        Send conversation-related notifications
        
        Args:
            conversation: Conversation instance
            notification_type: Type of notification
            data: Additional notification data
        """
        participants = conversation.participants.all()
        
        notification_data = {
            'type': 'conversation_notification',
            'conversation_id': str(conversation.id),
            'notification_type': notification_type,
            'data': data or {}
        }
        
        for participant in participants:
            self._send_to_user(participant.id, notification_data)
    
    def _send_to_user(self, user_id, notification_data):
        """
        Send notification to a specific user
        
        Args:
            user_id: User ID to send notification to
            notification_data: Notification data dictionary
        """
        if not self.channel_layer:
            return
        
        group_name = f'user_{user_id}'
        
        async_to_sync(self.channel_layer.group_send)(
            group_name,
            {
                'type': 'send_notification',
                'notification': notification_data
            }
        )
    
    def _get_relevant_users_for_presence(self, user):
        """
        Get list of users who should receive presence updates for a user
        
        Args:
            user: User whose presence changed
            
        Returns:
            list: List of user IDs who should receive the update
        """
        relevant_users = set()
        
        # Get users from active conversations
        conversations = Conversation.objects.filter(
            participants=user,
            is_active=True
        ).prefetch_related('participants')
        
        for conversation in conversations:
            for participant in conversation.participants.exclude(id=user.id):
                relevant_users.add(str(participant.id))
        
        return list(relevant_users)


# Global notification manager instance
notification_manager = NotificationManager()


def notify_new_message(message, exclude_user=None):
    """
    Convenience function to send new message notification
    
    Args:
        message: Message instance
        exclude_user: User to exclude from notifications
    """
    notification_manager.send_message_notification(message, exclude_user)


def notify_presence_change(user, status, last_seen=None):
    """
    Convenience function to send presence change notification
    
    Args:
        user: User whose presence changed
        status: New presence status
        last_seen: Last seen timestamp
    """
    notification_manager.send_presence_notification(user, status, last_seen)


def notify_typing_status(conversation, user, is_typing):
    """
    Convenience function to send typing status notification
    
    Args:
        conversation: Conversation instance
        user: User who is typing
        is_typing: Boolean indicating if user is typing
    """
    notification_manager.send_typing_notification(conversation, user, is_typing)


def notify_message_reaction(message, user, emoji, action):
    """
    Convenience function to send message reaction notification
    
    Args:
        message: Message instance
        user: User who reacted
        emoji: Reaction emoji
        action: 'add' or 'remove'
    """
    notification_manager.send_message_reaction_notification(message, user, emoji, action)


def notify_conversation_update(conversation, notification_type, data=None):
    """
    Convenience function to send conversation update notification
    
    Args:
        conversation: Conversation instance
        notification_type: Type of notification
        data: Additional notification data
    """
    notification_manager.send_conversation_notification(conversation, notification_type, data)
