# 📦 HeartGrid Requirements Guide

## Overview

HeartGrid provides multiple requirements files for different deployment scenarios:

- **`requirements.txt`** - Complete requirements with all optional packages
- **`requirements-minimal.txt`** - Essential packages only (recommended for basic setup)
- **`requirements-dev.txt`** - Development environment with testing tools
- **`requirements-prod.txt`** - Production environment with monitoring and security

## 🚀 Quick Start

### For Development
```bash
pip install -r requirements-minimal.txt
```

### For Production
```bash
pip install -r requirements-prod.txt
```

### For Development with Testing
```bash
pip install -r requirements-dev.txt
```

## 📋 Requirements Files Breakdown

### `requirements-minimal.txt` ✅ **RECOMMENDED**
**Essential packages for basic HeartGrid functionality:**

- **Django 5.0+** - Core web framework
- **Django REST Framework** - API functionality
- **Django Allauth** - Authentication system
- **Django OAuth Toolkit** - OAuth2 provider
- **Django Channels** - WebSocket support for real-time messaging
- **Daphne** - ASGI server for WebSocket handling
- **Pillow** - Image processing for user photos
- **Redis** - Caching and message broker
- **Gunicorn** - Production WSGI server

**Use this for:**
- Local development
- Basic production deployment
- Testing the platform

### `requirements-prod.txt` 🏭 **PRODUCTION**
**Includes minimal requirements plus production enhancements:**

- **PostgreSQL** - Production database
- **Sentry** - Error monitoring
- **Django Storages + AWS S3** - Cloud file storage
- **Django Redis** - Advanced caching
- **Security packages** - Rate limiting, enhanced security
- **Monitoring tools** - Health checks, background job monitoring
- **Cryptography** - Payment processing security

**Use this for:**
- Production deployment
- Staging environments
- Performance-critical applications

### `requirements-dev.txt` 🛠️ **DEVELOPMENT**
**Includes minimal requirements plus development tools:**

- **Pytest** - Testing framework
- **Code quality tools** - Black, Flake8, isort
- **Django Debug Toolbar** - Development debugging
- **Factory Boy + Faker** - Test data generation
- **Performance profiling** - Django Silk, memory profiler

**Use this for:**
- Development environment
- Running tests
- Code quality checks
- Performance analysis

### `requirements.txt` 📚 **COMPREHENSIVE**
**Complete package list with all optional enhancements:**

- All packages from other requirements files
- Additional Django extensions
- Extra security packages
- Advanced monitoring tools
- Optional integrations

**Use this for:**
- Full-featured development
- Enterprise deployments
- Maximum functionality

## 🔧 Installation Instructions

### 1. Create Virtual Environment
```bash
# Windows
python -m venv venv
venv\Scripts\activate

# Linux/Mac
python -m venv venv
source venv/bin/activate
```

### 2. Install Requirements
```bash
# Choose one based on your needs:

# Minimal (recommended for getting started)
pip install -r requirements-minimal.txt

# Development (for coding and testing)
pip install -r requirements-dev.txt

# Production (for deployment)
pip install -r requirements-prod.txt

# Complete (all features)
pip install -r requirements.txt
```

### 3. Verify Installation
```bash
pip check
python manage.py check
```

## 🌟 Key Features Enabled

### Real-time Communications
- **WebSocket messaging** via Django Channels
- **WebRTC calling** (voice/video) with signaling
- **User presence tracking** (online/offline status)
- **Real-time notifications**

### Authentication & Security
- **Email-based authentication** with Django Allauth
- **OAuth2 provider** for API access
- **Google OAuth integration**
- **Rate limiting** and security headers

### Media & File Handling
- **Image processing** with Pillow
- **File uploads** with validation
- **Cloud storage** support (S3)
- **Static file serving** with WhiteNoise

### Background Processing
- **Celery** for async tasks
- **Redis** for caching and message broker
- **Background job monitoring** with Flower

### API & Frontend
- **REST API** with Django REST Framework
- **CORS support** for frontend integration
- **WebSocket API** for real-time features
- **API documentation** with DRF Spectacular

## 🚨 Important Notes

### Python Version
- **Python 3.11+** required
- Tested with Python 3.11 and 3.12

### Database
- **SQLite** for development (included)
- **PostgreSQL** recommended for production

### Redis
- **Required** for WebSocket functionality
- **Required** for Celery background tasks
- Install Redis server separately

### Environment Variables
Create `.env` file with:
```env
SECRET_KEY=your-secret-key
DEBUG=True
DATABASE_URL=sqlite:///db.sqlite3
REDIS_URL=redis://localhost:6379/0
```

## 🔍 Troubleshooting

### Common Issues

**1. Redis Connection Error**
```bash
# Install and start Redis server
# Windows: Download from https://redis.io/download
# Linux: sudo apt-get install redis-server
# Mac: brew install redis
```

**2. Pillow Installation Issues**
```bash
# Install system dependencies first
# Ubuntu: sudo apt-get install libjpeg-dev zlib1g-dev
# CentOS: sudo yum install libjpeg-devel zlib-devel
```

**3. PostgreSQL Issues (Production)**
```bash
# Install PostgreSQL development headers
# Ubuntu: sudo apt-get install libpq-dev
# CentOS: sudo yum install postgresql-devel
```

### Dependency Conflicts
```bash
# Check for conflicts
pip check

# Upgrade pip if needed
pip install --upgrade pip

# Clear pip cache if issues persist
pip cache purge
```

## 📈 Performance Recommendations

### Development
- Use `requirements-minimal.txt` for faster installation
- Enable Django Debug Toolbar for performance insights
- Use SQLite for local development

### Production
- Use `requirements-prod.txt` with PostgreSQL
- Configure Redis for caching
- Set up Sentry for error monitoring
- Use cloud storage (S3) for media files

## 🎯 Next Steps

After installing requirements:

1. **Run migrations**: `python manage.py migrate`
2. **Create superuser**: `python manage.py createsuperuser`
3. **Start server**: `python manage.py runserver`
4. **Test communications**: `python test_communications_functionality.py`
5. **Access application**: http://127.0.0.1:8000

## 📞 Support

For issues with requirements or installation:
1. Check this guide first
2. Verify Python version (3.11+)
3. Ensure virtual environment is activated
4. Check system dependencies (Redis, PostgreSQL)
5. Review error messages for specific package issues

The HeartGrid platform is designed to work with minimal dependencies while providing extensive functionality for real-time communications and dating platform features! 🎉
